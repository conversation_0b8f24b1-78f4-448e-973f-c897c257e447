// lib/contact-utils.ts - Contact management utility functions

import { Contact, CONTACT_STATUSES, CONTACT_PRIORITIES } from "@/types/contact";

/**
 * Get status badge styling classes
 */
export const getStatusBadgeClasses = (status: string): string => {
  const statusConfig = CONTACT_STATUSES.find(s => s.value === status);
  const color = statusConfig?.color || "blue";
  
  const variants = {
    blue: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
    gray: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400",
    yellow: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",
    green: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
    red: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
  };
  
  return variants[color as keyof typeof variants] || variants.blue;
};

/**
 * Get priority badge styling classes
 */
export const getPriorityBadgeClasses = (priority: string): string => {
  const priorityConfig = CONTACT_PRIORITIES.find(p => p.value === priority);
  const color = priorityConfig?.color || "blue";
  
  const variants = {
    gray: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400",
    blue: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
    red: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
  };
  
  return variants[color as keyof typeof variants] || variants.blue;
};

/**
 * Get category icon component
 */
export const getCategoryIcon = (category: string) => {
  const icons = {
    general: "MessageSquare",
    technical: "AlertCircle", 
    bug: "XCircle",
    feature: "CheckCircle",
    business: "Users",
  };
  
  return icons[category as keyof typeof icons] || icons.general;
};

/**
 * Truncate message for display
 */
export const truncateMessage = (message: string, maxLength: number = 100): string => {
  if (message.length <= maxLength) return message;
  return message.substring(0, maxLength) + "...";
};

/**
 * Format date for display
 */
export const formatContactDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

/**
 * Format date for detailed view
 */
export const formatDetailedDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

/**
 * Get status display label
 */
export const getStatusLabel = (status: string): string => {
  const statusConfig = CONTACT_STATUSES.find(s => s.value === status);
  return statusConfig?.label || status;
};

/**
 * Get priority display label
 */
export const getPriorityLabel = (priority: string): string => {
  const priorityConfig = CONTACT_PRIORITIES.find(p => p.value === priority);
  return priorityConfig?.label || priority;
};

/**
 * Check if contact is unread
 */
export const isContactUnread = (contact: Contact): boolean => {
  return contact.status === "new";
};

/**
 * Check if contact is high priority
 */
export const isHighPriority = (contact: Contact): boolean => {
  return contact.priority === "high";
};

/**
 * Check if contact needs response
 */
export const needsResponse = (contact: Contact): boolean => {
  return !contact.responseSent && contact.status !== "resolved";
};

/**
 * Get contact age in days
 */
export const getContactAge = (contact: Contact): number => {
  const now = new Date();
  const created = new Date(contact.createdAt);
  const diffTime = Math.abs(now.getTime() - created.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

/**
 * Check if contact is overdue (more than 3 days without response)
 */
export const isContactOverdue = (contact: Contact): boolean => {
  return needsResponse(contact) && getContactAge(contact) > 3;
};

/**
 * Generate CSV content for contacts export
 */
export const generateContactsCSV = (contacts: Contact[]): string => {
  const headers = [
    "ID",
    "Name", 
    "Email",
    "Category",
    "Status",
    "Priority",
    "Assigned To",
    "Response Sent",
    "Created At",
    "Updated At",
    "Message"
  ];

  const rows = contacts.map(contact => [
    contact._id,
    contact.name,
    contact.email,
    contact.category,
    contact.status,
    contact.priority,
    contact.assignedTo ? `${contact.assignedTo.name} (${contact.assignedTo.email})` : "",
    contact.responseSent ? "Yes" : "No",
    new Date(contact.createdAt).toISOString(),
    new Date(contact.updatedAt).toISOString(),
    `"${contact.message.replace(/"/g, '""')}"` // Escape quotes in message
  ]);

  return [
    headers.join(","),
    ...rows.map(row => row.join(","))
  ].join("\n");
};

/**
 * Build contact filter query for API
 */
export const buildContactFilterQuery = (filters: any): URLSearchParams => {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value && value !== "all" && value !== "") {
      params.set(key, String(value));
    }
  });
  
  return params;
};

/**
 * Validate contact form data
 */
export const validateContactForm = (data: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!data.name || data.name.length < 2) {
    errors.push("Name must be at least 2 characters");
  }
  
  if (!data.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push("Please enter a valid email address");
  }
  
  if (!data.category) {
    errors.push("Please select a category");
  }
  
  if (!data.message || data.message.length < 10) {
    errors.push("Message must be at least 10 characters");
  }
  
  if (data.message && data.message.length > 5000) {
    errors.push("Message must be less than 5000 characters");
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};
