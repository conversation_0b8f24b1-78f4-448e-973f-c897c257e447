'use client';

import { useCallback, useRef, useState, useEffect } from 'react';
import { useLongPress } from 'use-long-press';
import { useSwipeable } from 'react-swipeable';

// Touch state interface
export interface TouchState {
  isPressed: boolean;
  isLongPressed: boolean;
  isSwiping: boolean;
  swipeDirection: 'left' | 'right' | 'up' | 'down' | null;
  touchStartTime: number | null;
}

// Touch configuration interface
export interface TouchConfig {
  longPressDelay?: number;
  swipeThreshold?: number;
  enableHapticFeedback?: boolean;
  preventScrollOnSwipe?: boolean;
}

// Touch handlers interface
export interface TouchHandlers {
  onTouchStart?: (event: TouchEvent) => void;
  onTouchEnd?: (event: TouchEvent) => void;
  onLongPress?: () => void;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  onTap?: () => void;
  onDoubleTap?: () => void;
}

// Default touch configuration
const DEFAULT_CONFIG: TouchConfig = {
  longPressDelay: 750,
  swipeThreshold: 50,
  enableHapticFeedback: true,
  preventScrollOnSwipe: false,
};

// Haptic feedback utility
export const hapticFeedback = {
  light: () => {
    if (typeof window !== 'undefined' && 'vibrate' in navigator) {
      navigator.vibrate(10);
    }
  },
  medium: () => {
    if (typeof window !== 'undefined' && 'vibrate' in navigator) {
      navigator.vibrate(20);
    }
  },
  heavy: () => {
    if (typeof window !== 'undefined' && 'vibrate' in navigator) {
      navigator.vibrate([30, 10, 30]);
    }
  },
  success: () => {
    if (typeof window !== 'undefined' && 'vibrate' in navigator) {
      navigator.vibrate([50, 25, 50]);
    }
  },
  error: () => {
    if (typeof window !== 'undefined' && 'vibrate' in navigator) {
      navigator.vibrate([100, 50, 100, 50, 100]);
    }
  },
};

// Touch detection utility
export const isTouchDevice = () => {
  if (typeof window === 'undefined') return false;
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

// Device type detection
export const getDeviceType = () => {
  if (typeof window === 'undefined') return 'desktop';
  
  const userAgent = navigator.userAgent.toLowerCase();
  const isTablet = /ipad|android(?!.*mobile)|tablet/.test(userAgent);
  const isMobile = /mobile|iphone|ipod|android.*mobile/.test(userAgent);
  
  if (isTablet) return 'tablet';
  if (isMobile) return 'mobile';
  return 'desktop';
};

// Main touch hook
export const useTouch = (handlers: TouchHandlers = {}, config: TouchConfig = {}) => {
  const mergedConfig = { ...DEFAULT_CONFIG, ...config };
  const [touchState, setTouchState] = useState<TouchState>({
    isPressed: false,
    isLongPressed: false,
    isSwiping: false,
    swipeDirection: null,
    touchStartTime: null,
  });

  const lastTapRef = useRef<number>(0);
  const touchStartRef = useRef<{ x: number; y: number } | null>(null);

  // Handle touch start
  const handleTouchStart = useCallback((event: TouchEvent) => {
    const touch = event.touches[0];
    touchStartRef.current = { x: touch.clientX, y: touch.clientY };
    
    setTouchState(prev => ({
      ...prev,
      isPressed: true,
      touchStartTime: Date.now(),
    }));

    if (mergedConfig.enableHapticFeedback) {
      hapticFeedback.light();
    }

    handlers.onTouchStart?.(event);
  }, [handlers.onTouchStart, mergedConfig.enableHapticFeedback]);

  // Handle touch end
  const handleTouchEnd = useCallback((event: TouchEvent) => {
    const touchEndTime = Date.now();
    const touchDuration = touchState.touchStartTime ? touchEndTime - touchState.touchStartTime : 0;

    setTouchState(prev => ({
      ...prev,
      isPressed: false,
      isSwiping: false,
      swipeDirection: null,
      touchStartTime: null,
    }));

    // Handle tap and double tap
    if (touchDuration < 200 && !touchState.isLongPressed) {
      const timeSinceLastTap = touchEndTime - lastTapRef.current;
      
      if (timeSinceLastTap < 300) {
        // Double tap
        handlers.onDoubleTap?.();
        if (mergedConfig.enableHapticFeedback) {
          hapticFeedback.medium();
        }
      } else {
        // Single tap
        setTimeout(() => {
          const newTimeSinceLastTap = Date.now() - lastTapRef.current;
          if (newTimeSinceLastTap >= 300) {
            handlers.onTap?.();
          }
        }, 300);
      }
      
      lastTapRef.current = touchEndTime;
    }

    handlers.onTouchEnd?.(event);
  }, [touchState, handlers.onTouchEnd, handlers.onTap, handlers.onDoubleTap, mergedConfig.enableHapticFeedback]);

  // Long press handler
  const longPressHandlers = useLongPress(
    () => {
      setTouchState(prev => ({ ...prev, isLongPressed: true }));
      if (mergedConfig.enableHapticFeedback) {
        hapticFeedback.heavy();
      }
      handlers.onLongPress?.();
    },
    {
      threshold: mergedConfig.longPressDelay,
      captureEvent: true,
      cancelOnMovement: true,
    }
  );

  // Swipe handlers
  const swipeHandlers = useSwipeable({
    onSwipeStart: () => {
      setTouchState(prev => ({ ...prev, isSwiping: true }));
    },
    onSwipedLeft: () => {
      setTouchState(prev => ({ ...prev, swipeDirection: 'left' }));
      if (mergedConfig.enableHapticFeedback) {
        hapticFeedback.medium();
      }
      handlers.onSwipeLeft?.();
    },
    onSwipedRight: () => {
      setTouchState(prev => ({ ...prev, swipeDirection: 'right' }));
      if (mergedConfig.enableHapticFeedback) {
        hapticFeedback.medium();
      }
      handlers.onSwipeRight?.();
    },
    onSwipedUp: () => {
      setTouchState(prev => ({ ...prev, swipeDirection: 'up' }));
      if (mergedConfig.enableHapticFeedback) {
        hapticFeedback.medium();
      }
      handlers.onSwipeUp?.();
    },
    onSwipedDown: () => {
      setTouchState(prev => ({ ...prev, swipeDirection: 'down' }));
      if (mergedConfig.enableHapticFeedback) {
        hapticFeedback.medium();
      }
      handlers.onSwipeDown?.();
    },
    delta: mergedConfig.swipeThreshold,
    preventScrollOnSwipe: mergedConfig.preventScrollOnSwipe,
    trackMouse: true, // Enable mouse tracking for desktop testing
  });

  // Combined touch handlers
  const touchHandlers = {
    ...longPressHandlers,
    ...swipeHandlers,
    onTouchStart: handleTouchStart,
    onTouchEnd: handleTouchEnd,
  };

  return {
    touchState,
    touchHandlers,
    isTouchDevice: isTouchDevice(),
    deviceType: getDeviceType(),
    hapticFeedback,
  };
};

// Specialized hooks for common patterns
export const useTouchCard = (onTap?: () => void, onLongPress?: () => void) => {
  return useTouch({
    onTap,
    onLongPress,
  });
};

export const useSwipeActions = (
  onSwipeLeft?: () => void,
  onSwipeRight?: () => void
) => {
  return useTouch({
    onSwipeLeft,
    onSwipeRight,
  });
};

export const usePullToRefresh = (onRefresh: () => void, threshold = 100) => {
  const [isPulling, setIsPulling] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);

  const swipeHandlers = useSwipeable({
    onSwiping: (eventData) => {
      if (eventData.dir === 'Down' && eventData.deltaY > 0) {
        setIsPulling(true);
        setPullDistance(Math.min(eventData.deltaY, threshold * 2));
      }
    },
    onSwipedDown: (eventData) => {
      if (eventData.deltaY > threshold) {
        onRefresh();
        hapticFeedback.success();
      }
      setIsPulling(false);
      setPullDistance(0);
    },
    delta: 10,
    preventScrollOnSwipe: false,
    trackMouse: true,
  });

  return {
    ...swipeHandlers,
    isPulling,
    pullDistance,
    pullProgress: Math.min(pullDistance / threshold, 1),
  };
};
