import mongoose, { Schema, model, models, Types } from "mongoose";

// Page View Analytics
export interface IPageView {
  _id?: Types.ObjectId;
  path: string;
  userId?: Types.ObjectId;
  sessionId: string;
  userAgent?: string;
  referrer?: string;
  timestamp: Date;
  duration?: number;
  ipAddress?: string;
}

const pageViewSchema = new Schema<IPageView>({
  path: {
    type: String,
    required: true,
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: "User",
  },
  sessionId: {
    type: String,
    required: true,
  },
  userAgent: String,
  referrer: String,
  timestamp: {
    type: Date,
    default: Date.now,
  },
  duration: Number,
  ipAddress: String,
});

// Create indexes separately to avoid duplication
pageViewSchema.index({ path: 1 });
pageViewSchema.index({ userId: 1 });
pageViewSchema.index({ sessionId: 1 });
pageViewSchema.index({ timestamp: -1 });

// User Activity
export interface IUserActivity {
  _id?: Types.ObjectId;
  userId: Types.ObjectId;
  action: string;
  entityType?: string;
  entityId?: string;
  details?: Record<string, any>;
  timestamp: Date;
}

const userActivitySchema = new Schema<IUserActivity>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  action: {
    type: String,
    required: true,
  },
  entityType: {
    type: String,
  },
  entityId: {
    type: String,
  },
  details: Schema.Types.Mixed,
  timestamp: {
    type: Date,
    default: Date.now,
  },
});

// Create indexes separately to avoid duplication
userActivitySchema.index({ userId: 1 });
userActivitySchema.index({ action: 1 });
userActivitySchema.index({ entityType: 1 });
userActivitySchema.index({ entityId: 1 });
userActivitySchema.index({ timestamp: -1 });

// Tool Usage
export interface IToolUsage {
  _id?: Types.ObjectId;
  toolId: string;
  toolName: string;
  userId?: Types.ObjectId;
  sessionId: string;
  timestamp: Date;
  duration?: number;
  status: "started" | "completed" | "failed";
  details?: Record<string, any>;
}

const toolUsageSchema = new Schema<IToolUsage>({
  toolId: {
    type: String,
    required: true,
  },
  toolName: {
    type: String,
    required: true,
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: "User",
  },
  sessionId: {
    type: String,
    required: true,
  },
  timestamp: {
    type: Date,
    default: Date.now,
  },
  duration: Number,
  status: {
    type: String,
    enum: ["started", "completed", "failed"],
    default: "started",
  },
  details: Schema.Types.Mixed,
});

// Create indexes separately to avoid duplication
toolUsageSchema.index({ toolId: 1 });
toolUsageSchema.index({ toolName: 1 });
toolUsageSchema.index({ userId: 1 });
toolUsageSchema.index({ sessionId: 1 });
toolUsageSchema.index({ timestamp: -1 });
toolUsageSchema.index({ status: 1 });

// Create models
export const PageView = mongoose.models.PageView || mongoose.model<IPageView>("PageView", pageViewSchema);
export const UserActivity = mongoose.models.UserActivity || mongoose.model<IUserActivity>("UserActivity", userActivitySchema);
export const ToolUsage = mongoose.models.ToolUsage || mongoose.model<IToolUsage>("ToolUsage", toolUsageSchema);
