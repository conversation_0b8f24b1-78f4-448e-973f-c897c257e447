'use client';

import { useEffect } from 'react';
import { initializePlatformOptimizations, getPlatformInfo } from '@/utils/platform';

export function PlatformInitializer() {
  useEffect(() => {
    // Initialize platform optimizations
    initializePlatformOptimizations();

    // Handle keyboard events for mobile
    const handleKeyboardShow = () => {
      const platformInfo = getPlatformInfo();
      if (platformInfo.isMobile) {
        document.documentElement.classList.add('keyboard-open');
        
        // Calculate keyboard height for iOS
        if (platformInfo.isIOS) {
          const initialHeight = window.innerHeight;
          const currentHeight = window.visualViewport?.height || window.innerHeight;
          const keyboardHeight = initialHeight - currentHeight;
          
          document.documentElement.style.setProperty('--keyboard-height', `${keyboardHeight}px`);
        }
      }
    };

    const handleKeyboardHide = () => {
      document.documentElement.classList.remove('keyboard-open');
      document.documentElement.style.removeProperty('--keyboard-height');
    };

    // Listen for keyboard events
    if ('visualViewport' in window) {
      window.visualViewport?.addEventListener('resize', () => {
        const platformInfo = getPlatformInfo();
        if (platformInfo.isMobile) {
          const initialHeight = window.innerHeight;
          const currentHeight = window.visualViewport?.height || window.innerHeight;
          
          if (currentHeight < initialHeight * 0.75) {
            handleKeyboardShow();
          } else {
            handleKeyboardHide();
          }
        }
      });
    }

    // Fallback for older browsers
    window.addEventListener('resize', () => {
      const platformInfo = getPlatformInfo();
      if (platformInfo.isMobile) {
        const currentHeight = window.innerHeight;
        const screenHeight = window.screen.height;
        
        if (currentHeight < screenHeight * 0.75) {
          handleKeyboardShow();
        } else {
          handleKeyboardHide();
        }
      }
    });

    // Handle orientation changes
    const handleOrientationChange = () => {
      // Delay to allow for orientation change to complete
      setTimeout(() => {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
      }, 100);
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleOrientationChange);

    // Cleanup
    return () => {
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('resize', handleOrientationChange);
      if ('visualViewport' in window) {
        window.visualViewport?.removeEventListener('resize', handleKeyboardShow);
      }
    };
  }, []);

  return null; // This component doesn't render anything
}

export default PlatformInitializer;
