import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import BlogPost from "@/models/BlogPost";
import { PageView } from "@/models/Analytics";
import mongoose from "mongoose";

// Helper function to parse date range
function parseDateRange(range: string) {
  const now = new Date();
  let startDate = new Date();
  
  switch (range) {
    case 'day':
      startDate.setDate(now.getDate() - 1);
      break;
    case 'week':
      startDate.setDate(now.getDate() - 7);
      break;
    case 'month':
      startDate.setMonth(now.getMonth() - 1);
      break;
    case 'year':
      startDate.setFullYear(now.getFullYear() - 1);
      break;
    default:
      startDate.setMonth(now.getMonth() - 1); // Default to month
  }
  
  return { startDate, endDate: now };
}

// GET blog statistics
export async function GET(request: NextRequest) {
  try {
    // Check authentication using request headers (set by middleware)
    const userRole = request.headers.get("x-user-role");
    
    // This endpoint can be accessed by both admin and regular users
    // but we'll provide more detailed stats for admins
    const isAdmin = userRole === "admin";
    
    await connectToDatabase();
    
    const { searchParams } = new URL(request.url);
    const range = searchParams.get("range") || "month";
    const { startDate, endDate } = parseDateRange(range);
    const postId = searchParams.get("postId");
    
    // Base query for time range
    const timeQuery = {
      createdAt: { $gte: startDate, $lte: endDate }
    };
    
    // If postId is provided, get stats for a specific post
    if (postId) {
      // Validate ID format
      if (!mongoose.Types.ObjectId.isValid(postId)) {
        return NextResponse.json(
          { error: "Invalid post ID format" },
          { status: 400 }
        );
      }
      
      // Check if post exists
      const post = await BlogPost.findById(postId);
      if (!post) {
        return NextResponse.json(
          { error: "Blog post not found" },
          { status: 404 }
        );
      }
      
      // Get post-specific stats
      const views = await PageView.countDocuments({
        path: { $regex: `/blog/${post.slug}` },
        timestamp: { $gte: startDate, $lte: endDate }
      });

      return NextResponse.json({
        post: {
          id: post._id,
          title: post.title,
          slug: post.slug,
          status: post.status,
          createdAt: post.createdAt,
          views
        }
      });
    }
    
    // Get overall blog stats
    const postsStats = await BlogPost.aggregate([
      {
        $facet: {
          total: [{ $count: "count" }],
          published: [
            { $match: { status: "published" } },
            { $count: "count" }
          ],
          draft: [
            { $match: { status: "draft" } },
            { $count: "count" }
          ],
          byMonth: [
            {
              $group: {
                _id: { $dateToString: { format: "%Y-%m", date: "$createdAt" } },
                count: { $sum: 1 }
              }
            },
            { $sort: { _id: 1 } },
            {
              $project: {
                _id: 0,
                month: "$_id",
                posts: "$count"
              }
            }
          ],
          popular: [
            { $match: { status: "published" } },
            { $sort: { viewCount: -1 } },
            { $limit: 5 },
            {
              $project: {
                _id: 1,
                title: 1,
                slug: 1,
                viewCount: 1
              }
            }
          ]
        }
      }
    ]);
    

    
    // Get view stats
    const viewsStats = await PageView.aggregate([
      {
        $match: {
          path: { $regex: "^/blog" },
          timestamp: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $facet: {
          total: [{ $count: "count" }],
          unique: [
            { $group: { _id: "$sessionId" } },
            { $count: "count" }
          ]
        }
      }
    ]);
    
    // Format the response
    const stats = {
      posts: {
        total: postsStats[0].total[0]?.count || 0,
        published: postsStats[0].published[0]?.count || 0,
        draft: postsStats[0].draft[0]?.count || 0,
        popular: postsStats[0].popular || []
      },

      views: {
        total: viewsStats[0].total[0]?.count || 0,
        unique: viewsStats[0].unique[0]?.count || 0
      },
      byMonth: postsStats[0].byMonth || []
    };
    
    return NextResponse.json(stats);
  } catch (error) {
    console.error("GET /api/blog/stats error:", error);
    return NextResponse.json(
      { error: "Failed to fetch blog statistics" },
      { status: 500 }
    );
  }
}
