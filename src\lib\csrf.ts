import { NextRequest, NextResponse } from 'next/server';
import { randomBytes, createHash, timingSafeEqual } from 'crypto';

// CSRF token configuration
const CSRF_TOKEN_LENGTH = 32;
const CSRF_TOKEN_EXPIRY = 60 * 60 * 1000; // 1 hour in milliseconds
const CSRF_COOKIE_NAME = 'csrf-token';
const CSRF_HEADER_NAME = 'x-csrf-token';

// Types
export interface CSRFToken {
  token: string;
  hash: string;
  expires: number;
}

export interface CSRFValidationResult {
  valid: boolean;
  error?: string;
}

// Generate a cryptographically secure CSRF token
export function generateCSRFToken(): CSRFToken {
  const token = randomBytes(CSRF_TOKEN_LENGTH).toString('hex');
  const hash = createHash('sha256').update(token).digest('hex');
  const expires = Date.now() + CSRF_TOKEN_EXPIRY;
  
  return {
    token,
    hash,
    expires,
  };
}

// Create CSRF token hash for comparison
export function createCSRFHash(token: string): string {
  return createHash('sha256').update(token).digest('hex');
}

// Validate CSRF token using timing-safe comparison
export function validateCSRFToken(
  providedToken: string,
  storedHash: string,
  expires: number
): CSRFValidationResult {
  // Check if token has expired
  if (Date.now() > expires) {
    return {
      valid: false,
      error: 'CSRF token has expired',
    };
  }
  
  // Check if token is provided
  if (!providedToken || typeof providedToken !== 'string') {
    return {
      valid: false,
      error: 'CSRF token is required',
    };
  }
  
  // Create hash of provided token
  const providedHash = createCSRFHash(providedToken);
  
  // Use timing-safe comparison to prevent timing attacks
  try {
    const providedBuffer = Buffer.from(providedHash, 'hex');
    const storedBuffer = Buffer.from(storedHash, 'hex');
    
    if (providedBuffer.length !== storedBuffer.length) {
      return {
        valid: false,
        error: 'Invalid CSRF token',
      };
    }
    
    const isValid = timingSafeEqual(providedBuffer, storedBuffer);
    
    return {
      valid: isValid,
      error: isValid ? undefined : 'Invalid CSRF token',
    };
  } catch (error) {
    return {
      valid: false,
      error: 'CSRF token validation failed',
    };
  }
}

// Extract CSRF token from request (header or body)
export function extractCSRFToken(request: NextRequest): string | null {
  // Try to get token from header first
  const headerToken = request.headers.get(CSRF_HEADER_NAME);
  if (headerToken) {
    return headerToken;
  }
  
  // For form submissions, token might be in the body
  // This will be handled by the API route that parses the body
  return null;
}

// Extract CSRF token from request body (for form submissions)
export function extractCSRFTokenFromBody(body: any): string | null {
  if (body && typeof body === 'object' && body.csrfToken) {
    return body.csrfToken;
  }
  return null;
}

// Get CSRF token from cookie
export function getCSRFTokenFromCookie(request: NextRequest): CSRFToken | null {
  const cookieValue = request.cookies.get(CSRF_COOKIE_NAME)?.value;
  
  if (!cookieValue) {
    return null;
  }
  
  try {
    return JSON.parse(cookieValue);
  } catch (error) {
    console.error('Failed to parse CSRF cookie:', error);
    return null;
  }
}

// Set CSRF token in cookie
export function setCSRFTokenCookie(response: NextResponse, csrfToken: CSRFToken): void {
  const cookieValue = JSON.stringify({
    hash: csrfToken.hash,
    expires: csrfToken.expires,
  });
  
  response.cookies.set(CSRF_COOKIE_NAME, cookieValue, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: CSRF_TOKEN_EXPIRY / 1000, // Convert to seconds
    path: '/',
  });
}

// Main CSRF validation function for API routes
export async function validateCSRF(request: NextRequest): Promise<CSRFValidationResult> {
  // Skip CSRF validation for GET, HEAD, OPTIONS requests
  if (['GET', 'HEAD', 'OPTIONS'].includes(request.method)) {
    return { valid: true };
  }
  
  // Get stored CSRF token from cookie
  const storedToken = getCSRFTokenFromCookie(request);
  if (!storedToken) {
    return {
      valid: false,
      error: 'CSRF token not found in cookie',
    };
  }
  
  // Extract CSRF token from request
  let providedToken = extractCSRFToken(request);
  
  // If not in header, try to get from body (for form submissions)
  if (!providedToken && request.headers.get('content-type')?.includes('application/json')) {
    try {
      const body = await request.json();
      providedToken = extractCSRFTokenFromBody(body);
      
      // Re-create request with parsed body for downstream handlers
      // Note: This is a limitation - we can't easily re-add the body to the request
      // So we'll need to handle this in the API route itself
    } catch (error) {
      // Body parsing failed, continue without token
    }
  }
  
  if (!providedToken) {
    return {
      valid: false,
      error: 'CSRF token not provided',
    };
  }
  
  // Validate the token
  return validateCSRFToken(providedToken, storedToken.hash, storedToken.expires);
}

// Generate and set new CSRF token (for API routes that need to provide tokens)
export function generateAndSetCSRFToken(response: NextResponse): string {
  const csrfToken = generateCSRFToken();
  setCSRFTokenCookie(response, csrfToken);
  return csrfToken.token;
}

// Middleware helper to add CSRF token to responses
export function addCSRFTokenToResponse(request: NextRequest, response: NextResponse): NextResponse {
  // Only add CSRF token for authenticated requests to admin or state-changing endpoints
  const pathname = request.nextUrl.pathname;
  const isAdminRoute = pathname.startsWith('/admin') || pathname.startsWith('/api/admin');
  const isContactRoute = pathname.startsWith('/contact') || pathname.startsWith('/api/contact');
  const isAuthenticatedUser = request.headers.get('x-user-id');
  
  if (isAdminRoute || (isContactRoute && isAuthenticatedUser)) {
    // Check if CSRF token exists and is valid
    const existingToken = getCSRFTokenFromCookie(request);
    
    if (!existingToken || Date.now() > existingToken.expires) {
      // Generate new token
      generateAndSetCSRFToken(response);
    }
  }
  
  return response;
}

// Create CSRF error response
export function createCSRFErrorResponse(error: string): Response {
  return new Response(
    JSON.stringify({
      error: 'CSRF validation failed',
      message: error,
      code: 'CSRF_INVALID',
    }),
    {
      status: 403,
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
}

// Helper to check if route requires CSRF protection
export function requiresCSRFProtection(pathname: string, method: string): boolean {
  // Skip for safe methods
  if (['GET', 'HEAD', 'OPTIONS'].includes(method)) {
    return false;
  }
  
  // Protect admin routes
  if (pathname.startsWith('/api/admin/')) {
    return true;
  }
  
  // Protect contact form
  if (pathname === '/api/contact') {
    return true;
  }
  
  // Protect file upload routes
  if (pathname.startsWith('/api/upload/')) {
    return true;
  }
  
  // Add other routes that need CSRF protection
  const protectedRoutes = [
    '/api/comments',
    '/api/blog',
  ];
  
  return protectedRoutes.some(route => pathname.startsWith(route));
}

// Client-side helper to get CSRF token for fetch requests
export function getCSRFTokenForClient(): string | null {
  if (typeof document === 'undefined') {
    return null;
  }
  
  // Try to get from meta tag (if set by server)
  const metaTag = document.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;
  if (metaTag) {
    return metaTag.content;
  }
  
  // Try to get from cookie (though this won't work due to httpOnly)
  // This is mainly for documentation purposes
  return null;
}
