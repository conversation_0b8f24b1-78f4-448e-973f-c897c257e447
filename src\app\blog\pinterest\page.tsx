'use client';

import { motion } from "framer-motion";
import { ArrowLef<PERSON>, Loader2 } from "lucide-react";
import { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { OptimizedPinterestLayout } from "@/components/blog/OptimizedPinterestLayout";
import { BlogSkeleton } from "@/components/blog/BlogSkeleton";
import useSWR from 'swr';

interface BlogPost {
  id?: string;
  _id?: string;
  title: string;
  excerpt?: string;
  description?: string;
  slug: string;
  featuredImage?: string;
  categoryName?: string;
  category?: string;
  publishedAt?: string;
  createdAt?: string;
  author: {
    name: string;
    email?: string;
  } | string;
  credit?: string;
}

// SWR fetcher function with optimized performance
const fetcher = async (url: string) => {
  const response = await fetch(url, {
    headers: {
      'Cache-Control': 'max-age=60', // Cache for 1 minute
    },
  });
  if (!response.ok) {
    throw new Error('Failed to fetch data');
  }
  const data = await response.json();
  return data;
};

export default function PinterestBlogPage() {
  const router = useRouter();
  const [page, setPage] = useState(1);
  const [allPosts, setAllPosts] = useState<BlogPost[]>([]);
  const [hasMore, setHasMore] = useState(true);

  // SWR for optimized data fetching with caching
  const { data, error, isLoading, mutate } = useSWR(
    `/api/blog?page=${page}&limit=9&status=published`,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      dedupingInterval: 60000, // 1 minute
    }
  );

  // Handle load more functionality
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoading) return;

    try {
      const nextPage = page + 1;
      const response = await fetcher(`/api/blog?page=${nextPage}&limit=9&status=published`);
      
      if (response.success && response.data.length > 0) {
        setAllPosts(prev => [...prev, ...response.data]);
        setPage(nextPage);
        
        // Check if there are more pages
        if (nextPage >= response.pagination.totalPages) {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error loading more posts:', error);
      setHasMore(false);
    }
  }, [page, hasMore, isLoading]);

  // Update posts when data changes
  useState(() => {
    if (data?.success && data.data) {
      if (page === 1) {
        setAllPosts(data.data);
      }
      setHasMore(page < data.pagination.totalPages);
    }
  }, [data, page]);

  const handleBackToBlog = () => {
    router.push('/blog');
  };

  // Combine initial posts with loaded posts
  const posts = page === 1 ? (data?.data || []) : allPosts;

  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />

      {/* Hero Section */}
      <motion.section
        className="py-16 px-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="max-w-7xl mx-auto">
          {/* Back Button */}
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1, duration: 0.5 }}
          >
            <Button
              onClick={handleBackToBlog}
              variant="outline"
              className="flex items-center gap-2 rounded-full px-6 py-3 transition-all duration-300 hover:scale-105"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Blog Home
            </Button>
          </motion.div>

          <div className="text-center">
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              📌 Pinterest Style Blog
            </motion.h1>

            <motion.p
              className="text-xl md:text-2xl mb-8 text-muted-foreground"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              Discover our latest articles in a beautiful masonry layout
            </motion.p>

            {/* Stats */}
            {data?.pagination && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="flex justify-center gap-4 mb-12"
              >
                <div className="flex items-center px-6 py-3 rounded-full bg-primary/10 text-primary border border-primary/20 backdrop-blur-sm">
                  📝 {data.pagination.total} Articles
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </motion.section>

      {/* Main Content Section */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          {/* Error State */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-20"
            >
              <div className="text-6xl mb-4">⚠️</div>
              <h3 className="text-xl font-medium mb-2 text-foreground">
                Failed to load articles
              </h3>
              <p className="text-muted-foreground mb-4">
                Please try refreshing the page
              </p>
              <Button onClick={() => mutate()} variant="outline">
                Retry
              </Button>
            </motion.div>
          )}

          {/* Loading State */}
          {isLoading && page === 1 && (
            <BlogSkeleton count={9} layout="masonry" />
          )}

          {/* Blog Posts */}
          {!error && !isLoading && posts.length > 0 && (
            <>
              <OptimizedPinterestLayout
                posts={posts}
                loading={false}
                showAnimation={true}
              />

              {/* Load More Button */}
              {hasMore && (
                <motion.div
                  className="text-center mt-12"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  <Button
                    onClick={loadMore}
                    disabled={isLoading}
                    className="px-8 py-3 rounded-full"
                    size="lg"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Loading more...
                      </>
                    ) : (
                      'Load More Articles'
                    )}
                  </Button>
                </motion.div>
              )}
            </>
          )}

          {/* Empty State */}
          {!error && !isLoading && posts.length === 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-20"
            >
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-xl font-medium mb-2 text-foreground">
                No articles found
              </h3>
              <p className="text-muted-foreground">
                Check back later for new content!
              </p>
            </motion.div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
}
