# Enhanced ToolBox Project Cleanup Script
# Comprehensive cleanup with verification and rollback capabilities
# Generated: 2025-06-29

param(
    [switch]$DryRun = $false,
    [switch]$Verbose = $false,
    [switch]$SkipConfirmation = $false,
    [switch]$CreateBackup = $true,
    [switch]$VerifyBuild = $true
)

# Color output functions
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }
function Write-Header { param($Message) Write-Host "`n$Message" -ForegroundColor Magenta -BackgroundColor Black }

# Initialize counters
$FilesRemoved = 0
$DirectoriesRemoved = 0
$TotalSizeFreed = 0
$ErrorCount = 0

Write-Header "🧹 Enhanced ToolBox Project Cleanup Script"
Write-Info "=============================================="

if ($DryRun) {
    Write-Warning "🔍 DRY RUN MODE - No files will be deleted"
}

# Create backup if requested
if ($CreateBackup -and -not $DryRun) {
    Write-Header "📦 Creating Backup"
    try {
        $backupBranch = "backup-cleanup-$(Get-Date -Format 'yyyy-MM-dd-HHmm')"
        git checkout -b $backupBranch 2>$null
        if ($LASTEXITCODE -eq 0) {
            git add . 2>$null
            git commit -m "Backup before cleanup - $(Get-Date)" 2>$null
            Write-Success "✅ Backup created on branch: $backupBranch"
            git checkout - 2>$null
        } else {
            Write-Warning "⚠️  Git backup failed - continuing without backup"
        }
    }
    catch {
        Write-Warning "⚠️  Could not create git backup: $($_.Exception.Message)"
    }
}

# Function to safely remove files/directories with enhanced verification
function Remove-SafelyWithVerification {
    param(
        [string]$Path,
        [string]$Description,
        [string]$Category = "File",
        [string]$Reason = "Development artifact"
    )
    
    if (-not (Test-Path $Path)) {
        if ($Verbose) { Write-Warning "⚠️  Path not found: $Path" }
        return
    }
    
    $item = Get-Item $Path
    $size = 0
    
    if ($item.PSIsContainer) {
        $size = (Get-ChildItem $Path -Recurse -File -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
        $Category = "Directory"
    } else {
        $size = $item.Length
    }
    
    $sizeKB = [math]::Round($size / 1KB, 2)
    
    Write-Info "📁 $Category`: $Description"
    Write-Info "   Path: $Path"
    Write-Info "   Size: $sizeKB KB"
    Write-Info "   Reason: $Reason"
    
    if ($DryRun) {
        Write-Warning "   [DRY RUN] Would remove this $($Category.ToLower())"
        return
    }
    
    if (-not $SkipConfirmation) {
        $confirmation = Read-Host "   Remove this $($Category.ToLower())? (y/N)"
        if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
            Write-Warning "   Skipped by user"
            return
        }
    }
    
    try {
        if ($item.PSIsContainer) {
            Remove-Item $Path -Recurse -Force -ErrorAction Stop
            $script:DirectoriesRemoved++
        } else {
            Remove-Item $Path -Force -ErrorAction Stop
            $script:FilesRemoved++
        }
        $script:TotalSizeFreed += $size
        Write-Success "   ✅ Removed successfully"
    }
    catch {
        Write-Error "   ❌ Failed to remove: $($_.Exception.Message)"
        $script:ErrorCount++
    }
}

Write-Header "🔴 PHASE 1: Test Files & Development Artifacts"

# Test page files (already removed, but included for completeness)
$testPages = @(
    @{ Path = "src/app/test"; Description = "Complete test directory"; Reason = "Development testing pages" },
    @{ Path = "src/app/test-hover"; Description = "Hover animation tests"; Reason = "Animation development testing" },
    @{ Path = "src/app/test-touch"; Description = "Touch interaction tests"; Reason = "Touch development testing" },
    @{ Path = "src/app/test-blog-api"; Description = "Blog API tests"; Reason = "API development testing" },
    @{ Path = "src/app/test-category"; Description = "Category tests"; Reason = "Category development testing" },
    @{ Path = "src/app/test-cloudinary"; Description = "Cloudinary tests"; Reason = "Image upload testing" },
    @{ Path = "src/app/test-minimal"; Description = "Minimal test page"; Reason = "Basic functionality testing" },
    @{ Path = "src/app/test-navigation"; Description = "Navigation tests"; Reason = "Navigation development testing" },
    @{ Path = "src/app/test-unified-routing"; Description = "Routing tests"; Reason = "Routing system testing" }
)

foreach ($page in $testPages) {
    Remove-SafelyWithVerification -Path $page.Path -Description $page.Description -Category "Directory" -Reason $page.Reason
}

# Test component files
$testComponents = @(
    @{ Path = "src/components/testing"; Description = "Testing components directory"; Reason = "Development testing components" },
    @{ Path = "src/components/test"; Description = "Test components"; Reason = "Development test utilities" }
)

foreach ($component in $testComponents) {
    Remove-SafelyWithVerification -Path $component.Path -Description $component.Description -Category "Directory" -Reason $component.Reason
}

# Development artifacts
$devArtifacts = @(
    @{ Path = "src/lib/tempo-mock.ts"; Description = "Mock Tempo devtools"; Reason = "Development mock implementation" },
    @{ Path = "src/app/blog-demo"; Description = "Blog demo page"; Reason = "Demo page not for production" },
    @{ Path = "src/app/home-redesign"; Description = "Home redesign experiments"; Reason = "Experimental design iterations" },
    @{ Path = "src/test-contact-system.ts"; Description = "Contact system test script"; Reason = "Development testing script" },
    @{ Path = "test-contact-system.js"; Description = "Duplicate contact test"; Reason = "Duplicate development script" },
    @{ Path = "hover-test.html"; Description = "Static hover test"; Reason = "Static development test file" },
    @{ Path = "test-blog-editor.md"; Description = "Blog editor test docs"; Reason = "Development documentation" }
)

foreach ($artifact in $devArtifacts) {
    $category = if (Test-Path $artifact.Path -PathType Container) { "Directory" } else { "File" }
    Remove-SafelyWithVerification -Path $artifact.Path -Description $artifact.Description -Category $category -Reason $artifact.Reason
}

Write-Header "🟡 PHASE 2: Historical Documentation"

# Historical documentation (already removed, but included for completeness)
$historicalDocs = @(
    @{ Path = "BLOG_COMPONENTS_FIXES.md"; Description = "Historical blog fixes"; Reason = "Outdated fix documentation" },
    @{ Path = "CATEGORY_DROPDOWN_FIXES.md"; Description = "Historical dropdown fixes"; Reason = "Outdated fix documentation" },
    @{ Path = "CATEGORY_DROPDOWN_IMPLEMENTATION.md"; Description = "Historical implementation"; Reason = "Superseded by current implementation" },
    @{ Path = "CONTACT_SYSTEM_FIXES.md"; Description = "Historical contact fixes"; Reason = "Outdated fix documentation" },
    @{ Path = "CONTACT_SYSTEM_VERIFICATION.md"; Description = "Historical verification"; Reason = "Outdated verification docs" },
    @{ Path = "HOVER_ANIMATIONS_FIX.md"; Description = "Historical hover fixes"; Reason = "Outdated animation fixes" },
    @{ Path = "blog-editor-fixes-summary.md"; Description = "Historical editor fixes"; Reason = "Outdated editor documentation" },
    @{ Path = "final-blog-editor-status.md"; Description = "Historical editor status"; Reason = "Outdated status documentation" },
    @{ Path = "complete-blog-solution-summary.md"; Description = "Historical solution summary"; Reason = "Superseded by current implementation" }
)

foreach ($doc in $historicalDocs) {
    Remove-SafelyWithVerification -Path $doc.Path -Description $doc.Description -Reason $doc.Reason
}

Write-Header "🟢 PHASE 3: Optional Advanced Cleanup"

# Check for potential duplicate files
$potentialDuplicates = @(
    @{ Path = "src/context/theme-context.tsx"; Description = "Potential duplicate theme context"; Reason = "May duplicate ThemeContext.tsx" },
    @{ Path = "src/types/components.d.ts"; Description = "Generic component types"; Reason = "Consider consolidating type files" },
    @{ Path = "src/types/css.d.ts"; Description = "CSS type definitions"; Reason = "May be unused" },
    @{ Path = "src/types/type.d.ts"; Description = "Generic type file"; Reason = "Generic naming, consider consolidation" }
)

Write-Warning "🔍 Checking for potential duplicates and consolidation opportunities:"
foreach ($duplicate in $potentialDuplicates) {
    if (Test-Path $duplicate.Path) {
        Write-Warning "   📄 Found: $($duplicate.Path) - $($duplicate.Description)"
        Write-Warning "      Reason: $($duplicate.Reason)"
        Write-Warning "      Action: Manual review recommended"
    }
}

# Build verification if requested
if ($VerifyBuild -and -not $DryRun) {
    Write-Header "🔧 Build Verification"
    Write-Info "Running build verification to ensure cleanup didn't break anything..."
    
    try {
        $buildOutput = npm run build 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "✅ Build verification passed!"
        } else {
            Write-Error "❌ Build verification failed!"
            Write-Error "Build output: $buildOutput"
            Write-Warning "Consider rolling back changes if build issues persist"
        }
    }
    catch {
        Write-Error "❌ Could not run build verification: $($_.Exception.Message)"
    }
}

Write-Header "📊 CLEANUP SUMMARY"

if ($DryRun) {
    Write-Info "🔍 DRY RUN COMPLETED - No files were actually removed"
    Write-Info "Run without -DryRun flag to perform actual cleanup"
} else {
    Write-Success "✅ Files removed: $FilesRemoved"
    Write-Success "✅ Directories removed: $DirectoriesRemoved"
    $sizeMB = [math]::Round($TotalSizeFreed / 1MB, 2)
    Write-Success "✅ Total space freed: $sizeMB MB"
    
    if ($ErrorCount -gt 0) {
        Write-Warning "⚠️  Errors encountered: $ErrorCount"
    } else {
        Write-Success "✅ No errors encountered"
    }
}

Write-Header "🎯 POST-CLEANUP RECOMMENDATIONS"

Write-Info "1. 🧪 Test Application Functionality:"
Write-Info "   • Homepage and navigation"
Write-Info "   • PDF tools and calculators"
Write-Info "   • Blog system and admin panel"
Write-Info "   • User authentication"

Write-Info "2. 🚀 Performance Improvements:"
Write-Info "   • Run bundle analyzer: npm run analyze"
Write-Info "   • Monitor build times and hot reload speed"
Write-Info "   • Check for further optimization opportunities"

Write-Info "3. 🔒 Security Enhancements:"
Write-Info "   • Implement CSP headers"
Write-Info "   • Add comprehensive rate limiting"
Write-Info "   • Consider security header middleware"

Write-Info "4. 📈 Future Maintenance:"
Write-Info "   • Set up automated testing to replace manual tests"
Write-Info "   • Implement CI/CD pipeline"
Write-Info "   • Regular dependency updates"

if (-not $DryRun) {
    Write-Success "`n🎉 Enhanced cleanup completed successfully!"
    Write-Info "Your ToolBox project is now optimized, cleaner, and production-ready."
    
    if ($CreateBackup) {
        Write-Info "💾 Backup branch created for safety - you can rollback if needed."
    }
}
