import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import Category from "@/models/Category";
import BlogPost from "@/models/BlogPost";

// GET single category
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectToDatabase();
    const { id } = await params;

    const category = await Category.findById(id);
    
    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    // Get post count for this category
    const postCount = await BlogPost.countDocuments({
      categoryId: id,
      status: "published",
      visibility: "public"
    });

    return NextResponse.json({
      success: true,
      data: {
        ...category.toObject(),
        count: postCount
      }
    });
  } catch (error) {
    console.error("Error fetching category:", error);
    return NextResponse.json(
      { error: "Failed to fetch category" },
      { status: 500 }
    );
  }
}

// DELETE category
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectToDatabase();
    const { id } = await params;

    // Check authentication using request headers (set by middleware)
    const userRole = request.headers.get("x-user-role");

    // Only admin users can delete categories
    if (userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    // Check if category exists
    const category = await Category.findById(id);
    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    // Check if any blog posts are using this category
    const postsUsingCategory = await BlogPost.countDocuments({
      categoryId: id
    });

    if (postsUsingCategory > 0) {
      return NextResponse.json(
        { 
          error: "Cannot delete category", 
          message: `This category is being used by ${postsUsingCategory} blog post(s). Please reassign or delete those posts first.`,
          postsCount: postsUsingCategory
        },
        { status: 409 }
      );
    }

    // Delete the category
    await Category.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: "Category deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting category:", error);
    return NextResponse.json(
      { error: "Failed to delete category" },
      { status: 500 }
    );
  }
}

// PUT/PATCH update category
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectToDatabase();
    const { id } = await params;

    // Check authentication using request headers (set by middleware)
    const userRole = request.headers.get("x-user-role");

    // Only admin users can update categories
    if (userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, description } = body;

    if (!name || name.trim().length < 2) {
      return NextResponse.json(
        { error: "Category name must be at least 2 characters long" },
        { status: 400 }
      );
    }

    // Check if category exists
    const category = await Category.findById(id);
    if (!category) {
      return NextResponse.json(
        { error: "Category not found" },
        { status: 404 }
      );
    }

    // Check if another category with the same name exists (case-insensitive)
    const existingCategory = await Category.findOne({
      name: { $regex: new RegExp(`^${name.trim()}$`, 'i') },
      _id: { $ne: id }
    });

    if (existingCategory) {
      return NextResponse.json(
        { error: "A category with this name already exists" },
        { status: 409 }
      );
    }

    // Update the category
    const updatedCategory = await Category.findByIdAndUpdate(
      id,
      { 
        name: name.trim(),
        description: description?.trim() || ""
      },
      { new: true, runValidators: true }
    );

    // If category name changed, update all blog posts using this category
    if (category.name !== name.trim()) {
      await BlogPost.updateMany(
        { categoryId: id },
        { categoryName: name.trim() }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedCategory
    });
  } catch (error) {
    console.error("Error updating category:", error);
    return NextResponse.json(
      { error: "Failed to update category" },
      { status: 500 }
    );
  }
}
