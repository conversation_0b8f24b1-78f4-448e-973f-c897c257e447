"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { ArrowLeft, FileText, Scale, AlertTriangle, Shield, Users, Gavel } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import GlobalSEO from "@/components/seo/GlobalSEO";

export default function TermsPage() {
  const router = useRouter();

  const sections = [
    {
      id: "acceptance",
      title: "Acceptance of Terms",
      icon: <FileText className="h-5 w-5" />,
      content: [
        {
          subtitle: "Agreement to Terms",
          text: "By accessing and using ToolCrush, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service."
        },
        {
          subtitle: "Modifications",
          text: "We reserve the right to modify these terms at any time. Changes will be effective immediately upon posting. Your continued use of the platform constitutes acceptance of any modifications."
        }
      ]
    },
    {
      id: "platform-usage",
      title: "Platform Usage Rights",
      icon: <Users className="h-5 w-5" />,
      content: [
        {
          subtitle: "License to Use",
          text: "We grant you a limited, non-exclusive, non-transferable license to access and use our tools and calculators for personal and commercial purposes, subject to these terms."
        },
        {
          subtitle: "Usage Restrictions",
          text: "You may not reverse engineer, decompile, or attempt to extract source code from our platform. Automated scraping, bulk downloading, or systematic data extraction is prohibited."
        },
        {
          subtitle: "Fair Use Policy",
          text: "We implement reasonable usage limits to ensure platform availability for all users. Excessive usage that impacts service performance may result in temporary restrictions."
        }
      ]
    },
    {
      id: "user-accounts",
      title: "User Accounts and Responsibilities",
      icon: <Shield className="h-5 w-5" />,
      content: [
        {
          subtitle: "Account Security",
          text: "You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account."
        },
        {
          subtitle: "Accurate Information",
          text: "You agree to provide accurate, current, and complete information during registration and to update such information to keep it accurate and current."
        },
        {
          subtitle: "Prohibited Activities",
          text: "You may not use the platform for illegal activities, to transmit harmful content, or to violate the rights of others. Spam, harassment, and malicious behavior are strictly prohibited."
        }
      ]
    },
    {
      id: "content-ownership",
      title: "Content and Intellectual Property",
      icon: <Scale className="h-5 w-5" />,
      content: [
        {
          subtitle: "Platform Content",
          text: "All content, features, and functionality of ToolCrush, including but not limited to text, graphics, logos, and software, are owned by us and protected by copyright and other intellectual property laws."
        },
        {
          subtitle: "User-Generated Content",
          text: "You retain ownership of content you create using our tools. However, you grant us a license to use, store, and process this content to provide our services."
        },
        {
          subtitle: "Third-Party Content",
          text: "Some tools may integrate with third-party services. Use of such integrations is subject to the respective third-party terms and conditions."
        }
      ]
    },
    {
      id: "ai-generated-content",
      title: "AI-Generated Content and Limitations",
      icon: <AlertTriangle className="h-5 w-5" />,
      content: [
        {
          subtitle: "AI Tool Disclaimers",
          text: "Our AI-powered tools provide estimates and suggestions based on algorithms. Results should not be considered as professional advice and may contain inaccuracies."
        },
        {
          subtitle: "Calculation Accuracy",
          text: "While we strive for accuracy in our calculators and tools, we do not guarantee the precision of results. Users should verify important calculations independently."
        },
        {
          subtitle: "Professional Consultation",
          text: "For financial, legal, health, or other professional matters, always consult with qualified professionals. Our tools are for informational purposes only."
        }
      ]
    },
    {
      id: "liability-disclaimers",
      title: "Liability and Disclaimers",
      icon: <Gavel className="h-5 w-5" />,
      content: [
        {
          subtitle: "Service Availability",
          text: "We strive to maintain platform availability but do not guarantee uninterrupted service. Maintenance, updates, or technical issues may cause temporary unavailability."
        },
        {
          subtitle: "Limitation of Liability",
          text: "To the maximum extent permitted by law, we shall not be liable for any indirect, incidental, special, or consequential damages arising from your use of the platform."
        },
        {
          subtitle: "Indemnification",
          text: "You agree to indemnify and hold us harmless from any claims, damages, or expenses arising from your use of the platform or violation of these terms."
        }
      ]
    }
  ];

  return (
    <>
      <GlobalSEO
        pageTitle="Terms and Conditions"
        pageDescription="Read our terms and conditions for using ToolCrush platform. Understand your rights and responsibilities when using our tools and services."
        pageUrl="/terms"
        pageType="article"
      />
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
      >
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              onClick={() => router.back()}
              className="flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Version 1.0 • Last updated: {new Date().toLocaleDateString()}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="max-w-4xl mx-auto"
        >
          {/* Title Section */}
          <div className="text-center mb-12">
            <motion.div
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-6"
            >
              <Scale className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </motion.div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Terms and Conditions
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Please read these terms carefully before using our platform and services.
            </p>
          </div>

          {/* Table of Contents */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-8"
          >
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Table of Contents
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {sections.map((section, index) => (
                <motion.a
                  key={section.id}
                  href={`#${section.id}`}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
                  className="flex items-center p-2 rounded-md text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  {section.icon}
                  <span className="ml-2">{section.title}</span>
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Sections */}
          <div className="space-y-8">
            {sections.map((section, sectionIndex) => (
              <motion.section
                key={section.id}
                id={section.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.5 + sectionIndex * 0.1 }}
                className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-8"
              >
                <div className="flex items-center mb-6">
                  <div className="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg mr-4">
                    {section.icon}
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {section.title}
                  </h2>
                </div>
                
                <div className="space-y-6">
                  {section.content.map((item, itemIndex) => (
                    <motion.div
                      key={itemIndex}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.6 + sectionIndex * 0.1 + itemIndex * 0.05 }}
                    >
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {item.subtitle}
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                        {item.text}
                      </p>
                    </motion.div>
                  ))}
                </div>
              </motion.section>
            ))}
          </div>

          {/* Termination and Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800 p-8 mt-12"
          >
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-red-600 dark:text-red-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Account Termination
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto">
                We reserve the right to terminate or suspend accounts that violate these terms. 
                Users may also delete their accounts at any time through account settings.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild>
                  <Link href="/contact">
                    Contact Support
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/privacy-policy">
                    Privacy Policy
                  </Link>
                </Button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
      </div>
    </>
  );
}
