// hooks/useContactUpdates.ts - Custom hook for real-time contact updates

import { useState, useEffect, useCallback, useRef } from 'react';
import { Contact, ContactStats } from '@/types/contact';

interface UseContactUpdatesOptions {
  enabled?: boolean;
  interval?: number; // in milliseconds
  onNewContact?: (contact: Contact) => void;
  onStatsUpdate?: (stats: ContactStats) => void;
}

interface UseContactUpdatesReturn {
  unreadCount: number;
  stats: ContactStats | null;
  isConnected: boolean;
  lastUpdate: Date | null;
  forceRefresh: () => void;
}

/**
 * Custom hook for real-time contact updates
 * Polls the API for new contacts and statistics updates
 */
export function useContactUpdates(options: UseContactUpdatesOptions = {}): UseContactUpdatesReturn {
  const {
    enabled = true,
    interval = 30000, // 30 seconds default
    onNewContact,
    onStatsUpdate
  } = options;

  const [unreadCount, setUnreadCount] = useState(0);
  const [stats, setStats] = useState<ContactStats | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastStatsRef = useRef<ContactStats | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/contact?status=new&limit=1');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success && data.data.stats) {
        const newStats = data.data.stats;
        
        // Check for new contacts
        if (lastStatsRef.current && onNewContact) {
          const previousTotal = lastStatsRef.current.total;
          const currentTotal = newStats.total;
          
          if (currentTotal > previousTotal) {
            // Fetch the latest contact to trigger callback
            try {
              const latestResponse = await fetch('/api/admin/contact?limit=1&sortBy=createdAt&sortOrder=desc');
              if (latestResponse.ok) {
                const latestData = await latestResponse.json();
                if (latestData.success && latestData.data.contacts.length > 0) {
                  onNewContact(latestData.data.contacts[0]);
                }
              }
            } catch (error) {
              console.warn('Failed to fetch latest contact:', error);
            }
          }
        }

        setStats(newStats);
        setUnreadCount(newStats.new || 0);
        setIsConnected(true);
        setLastUpdate(new Date());
        
        // Trigger stats update callback
        if (onStatsUpdate) {
          onStatsUpdate(newStats);
        }
        
        lastStatsRef.current = newStats;
      }
    } catch (error) {
      console.error('Error fetching contact stats:', error);
      setIsConnected(false);
    }
  }, [onNewContact, onStatsUpdate]);

  const forceRefresh = useCallback(() => {
    fetchStats();
  }, [fetchStats]);

  // Set up polling interval
  useEffect(() => {
    if (!enabled) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Initial fetch
    fetchStats();

    // Set up interval
    intervalRef.current = setInterval(fetchStats, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [enabled, interval, fetchStats]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    unreadCount,
    stats,
    isConnected,
    lastUpdate,
    forceRefresh
  };
}

/**
 * Hook for contact list with real-time updates
 */
export function useContactList(filters: any = {}) {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    limit: 10,
    hasNextPage: false,
    hasPrevPage: false,
  });

  const fetchContacts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== 'all' && value !== '') {
          params.set(key, String(value));
        }
      });

      const response = await fetch(`/api/admin/contact?${params}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch contacts: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setContacts(data.data.contacts);
        setPagination(data.data.pagination);
      } else {
        throw new Error(data.error || 'Failed to fetch contacts');
      }
    } catch (err: any) {
      console.error('Error fetching contacts:', err);
      setError(err.message || 'Failed to fetch contacts');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Fetch contacts when filters change
  useEffect(() => {
    fetchContacts();
  }, [fetchContacts]);

  return {
    contacts,
    loading,
    error,
    pagination,
    refetch: fetchContacts
  };
}

/**
 * Hook for managing contact operations
 */
export function useContactOperations() {
  const [loading, setLoading] = useState(false);

  const updateContact = useCallback(async (id: string, data: any) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/contact/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`Failed to update contact: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteContact = useCallback(async (id: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/admin/contact/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete contact: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkOperation = useCallback(async (action: string, contactIds: string[], data?: any) => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/contact/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          contactIds,
          data,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to perform bulk operation: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    updateContact,
    deleteContact,
    bulkOperation
  };
}
