import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/mongo";
import Image from "@/models/Image";
import { z } from "zod";

// Validation schema for creating images
const ImageCreateSchema = z.object({
  url: z.string().url("Must be a valid URL"),
  publicId: z.string().optional(),
  fileName: z.string().optional(),
  location: z.enum(["local", "cloud"]),
  type: z.enum(["file", "url"]),
  originalUrl: z.string().url().optional(),
  width: z.number().min(0).optional(),
  height: z.number().min(0).optional(),
  format: z.string().optional(),
  bytes: z.number().min(0).optional(),
  alt: z.string().max(200).optional(),
  title: z.string().max(200).optional(),
  tags: z.array(z.string()).optional(),
  folder: z.string().optional(),
});

// GET /api/images - Fetch images with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const location = searchParams.get("location") as "local" | "cloud" | null;
    const type = searchParams.get("type") as "file" | "url" | null;
    const userId = searchParams.get("userId");
    const tags = searchParams.get("tags")?.split(",");
    const folder = searchParams.get("folder");

    // Build query
    const query: any = {};
    
    if (location) query.location = location;
    if (type) query.type = type;
    if (userId) query.uploadedBy = userId;
    if (tags && tags.length > 0) query.tags = { $in: tags };
    if (folder) query.folder = folder;

    // Calculate skip for pagination
    const skip = (page - 1) * limit;

    // Execute query with pagination
    const [images, total] = await Promise.all([
      Image.find(query)
        .populate('uploadedBy', 'name email')
        .sort({ uploadedAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Image.countDocuments(query)
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      images,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNextPage,
        hasPrevPage,
      },
    });

  } catch (error) {
    console.error("GET /api/images error:", error);
    return NextResponse.json(
      { error: "Failed to fetch images" },
      { status: 500 }
    );
  }
}

// POST /api/images - Create a new image record
export async function POST(request: NextRequest) {
  try {
    await connectToDatabase();

    // Check authentication
    const userRole = request.headers.get("x-user-role");
    const userId = request.headers.get("x-user-id");

    if (!userId) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate input
    const validation = ImageCreateSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error },
        { status: 400 }
      );
    }

    const imageData = validation.data;

    // Create new image record
    const newImage = new Image({
      ...imageData,
      uploadedBy: userId,
      uploadedAt: new Date(),
    });

    await newImage.save();

    // Populate the uploadedBy field for response
    await newImage.populate('uploadedBy', 'name email');

    return NextResponse.json({
      success: true,
      image: newImage,
      message: "Image record created successfully",
    }, { status: 201 });

  } catch (error) {
    console.error("POST /api/images error:", error);
    return NextResponse.json(
      { error: "Failed to create image record" },
      { status: 500 }
    );
  }
}

// DELETE /api/images/[id] - Delete an image record
export async function DELETE(request: NextRequest) {
  try {
    await connectToDatabase();

    // Check authentication
    const userRole = request.headers.get("x-user-role");
    const userId = request.headers.get("x-user-id");

    if (!userId) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const imageId = searchParams.get("id");

    if (!imageId) {
      return NextResponse.json(
        { error: "Image ID is required" },
        { status: 400 }
      );
    }

    // Find the image
    const image = await Image.findById(imageId);
    if (!image) {
      return NextResponse.json(
        { error: "Image not found" },
        { status: 404 }
      );
    }

    // Check if user owns the image or is admin
    if (image.uploadedBy.toString() !== userId && userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - You can only delete your own images" },
        { status: 403 }
      );
    }

    // Delete the image record
    await Image.findByIdAndDelete(imageId);

    return NextResponse.json({
      success: true,
      message: "Image record deleted successfully",
    });

  } catch (error) {
    console.error("DELETE /api/images error:", error);
    return NextResponse.json(
      { error: "Failed to delete image record" },
      { status: 500 }
    );
  }
}

// PUT /api/images/[id] - Update an image record
export async function PUT(request: NextRequest) {
  try {
    await connectToDatabase();

    // Check authentication
    const userRole = request.headers.get("x-user-role");
    const userId = request.headers.get("x-user-id");

    if (!userId) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const imageId = searchParams.get("id");

    if (!imageId) {
      return NextResponse.json(
        { error: "Image ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();

    // Validate input (partial update)
    const updateSchema = ImageCreateSchema.partial();
    const validation = updateSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error },
        { status: 400 }
      );
    }

    // Find the image
    const image = await Image.findById(imageId);
    if (!image) {
      return NextResponse.json(
        { error: "Image not found" },
        { status: 404 }
      );
    }

    // Check if user owns the image or is admin
    if (image.uploadedBy.toString() !== userId && userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - You can only update your own images" },
        { status: 403 }
      );
    }

    // Update the image
    const updatedImage = await Image.findByIdAndUpdate(
      imageId,
      validation.data,
      { new: true, runValidators: true }
    ).populate('uploadedBy', 'name email');

    return NextResponse.json({
      success: true,
      image: updatedImage,
      message: "Image record updated successfully",
    });

  } catch (error) {
    console.error("PUT /api/images error:", error);
    return NextResponse.json(
      { error: "Failed to update image record" },
      { status: 500 }
    );
  }
}
