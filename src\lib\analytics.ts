"use client";

import { v4 as uuidv4 } from 'uuid';

// Generate or retrieve session ID
function getSessionId(): string {
  if (typeof window === 'undefined') return '';

  let sessionId = sessionStorage.getItem('analytics_session_id');

  if (!sessionId) {
    sessionId = uuidv4();
    sessionStorage.setItem('analytics_session_id', sessionId);
  }

  return sessionId;
}

// Get referrer information
function getReferrer(): string {
  if (typeof document === 'undefined') return '';

  const referrer = document.referrer;

  if (!referrer) return 'direct';

  try {
    const url = new URL(referrer);
    return url.hostname;
  } catch (e) {
    return 'unknown';
  }
}

// ANALYTICS COMPLETELY DISABLED FOR PERFORMANCE
export async function trackPageView(path: string): Promise<void> {
  // No-op function - analytics disabled
  return;

  try {
    const sessionId = getSessionId();

    // console.log(`Tracking page view: ${path}`);

    // First, try to get the user ID from the /api/auth/me endpoint
    let userId = 'anonymous';

    try {
      const userResponse = await fetch('/api/auth/me', {
        credentials: 'include',
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (userResponse.ok) {
        const userData = await userResponse.json();
        if (userData.user && userData.user.id) {
          userId = userData.user.id;
          console.log(`Analytics: Found user ID for tracking: ${userId}`);
        }
      }
    } catch (userError) {
      console.error('Failed to get user ID for analytics:', userError);
    }

    // Add credentials: 'include' to ensure cookies are sent with the request
    const response = await fetch('/api/analytics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Important for sending auth cookies
      body: JSON.stringify({
        type: 'pageView',
        data: {
          path,
          userId, // Include the user ID we retrieved
          sessionId,
          referrer: getReferrer(),
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(), // Add timestamp on client side
        },
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Error response from analytics API:', errorData);
      throw new Error(`Analytics API returned ${response.status}`);
    }

    console.log(`Successfully tracked page view: ${path}`);
  } catch (error) {
    console.error('Failed to track page view:', error);
  }
}

export async function trackUserActivity(action: string, entityType?: string, entityId?: string, details?: Record<string, any>): Promise<void> {
  // No-op function - analytics disabled
  return;

  try {
    const sessionId = getSessionId();

    // console.log(`Tracking user activity: ${action} on ${entityType || 'unknown'}`);

    // First, try to get the user ID from the /api/auth/me endpoint
    let userId = 'anonymous';

    try {
      const userResponse = await fetch('/api/auth/me', {
        credentials: 'include',
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (userResponse.ok) {
        const userData = await userResponse.json();
        if (userData.user && userData.user.id) {
          userId = userData.user.id;
          console.log(`Analytics: Found user ID for activity tracking: ${userId}`);
        }
      }
    } catch (userError) {
      console.error('Failed to get user ID for analytics:', userError);
    }

    const response = await fetch('/api/analytics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Important for sending auth cookies
      body: JSON.stringify({
        type: 'userActivity',
        data: {
          action,
          entityType,
          entityId,
          userId, // Include the user ID we retrieved
          sessionId,
          details,
          timestamp: new Date().toISOString(),
        },
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Error response from analytics API:', errorData);
      throw new Error(`Analytics API returned ${response.status}`);
    }

    console.log(`Successfully tracked user activity: ${action}`);
  } catch (error) {
    console.error('Failed to track user activity:', error);
  }
}

export async function trackToolUsage(toolId: string, toolName: string, status: 'started' | 'completed' | 'failed', details?: Record<string, any>): Promise<void> {
  // No-op function - analytics disabled
  return;

  try {
    const sessionId = getSessionId();

    // console.log(`Tracking tool usage: ${toolName} (${status})`);

    // First, try to get the user ID from the /api/auth/me endpoint
    let userId = 'anonymous';

    try {
      const userResponse = await fetch('/api/auth/me', {
        credentials: 'include',
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (userResponse.ok) {
        const userData = await userResponse.json();
        if (userData.user && userData.user.id) {
          userId = userData.user.id;
          console.log(`Analytics: Found user ID for tool usage tracking: ${userId}`);
        }
      }
    } catch (userError) {
      console.error('Failed to get user ID for analytics:', userError);
    }

    // Add credentials: 'include' to ensure cookies are sent with the request
    // This helps with authentication if the user is logged in
    const response = await fetch('/api/analytics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Important for sending auth cookies
      body: JSON.stringify({
        type: 'toolUsage',
        data: {
          toolId,
          toolName,
          userId, // Include the user ID we retrieved
          sessionId,
          status,
          details,
          timestamp: new Date().toISOString(), // Add timestamp on client side
        },
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Error response from analytics API:', errorData);
      throw new Error(`Analytics API returned ${response.status}`);
    }

    console.log(`Successfully tracked tool usage: ${toolName}`);
  } catch (error) {
    console.error('Failed to track tool usage:', error);
  }
}

// Analytics hook for React components
export function useAnalytics() {
  return {
    trackPageView,
    trackUserActivity,
    trackToolUsage,
  };
}
