'use client';

import Link from "next/link";
import { motion } from "framer-motion";
import {
  FiArrowRight
} from "react-icons/fi";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";

import { getPopularCalculators } from "@/data/calculators";
import { ALL_TOOLS } from "@/data/tools";
import HeroSection from "@/components/layout/hero/HeroSection";
import RecentBlogPosts from "@/components/home/<USER>";
import UnifiedCard from "@/components/ui/UnifiedCard";

export default function Home() {
  // Get exactly 8 tools for homepage
  const homepageTools = ALL_TOOLS.filter(tool => tool.popular).slice(0, 8);

  // Get exactly 8 calculators for homepage
  const homepageCalculators = getPopularCalculators(8);

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <main className="min-h-screen bg-background text-foreground transition-all duration-500 ease-in-out">
      <Header/>
      <section>

      <HeroSection/>
      </section>

      {/* <section className="py-16 bg-gradient-to-r from-blue-500 to-blue-700 text-white">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="container mx-auto px-4 text-center"
        >
          <h1 className="text-4xl md:text-5xl font-bold mb-4">All-in-one PDF Solution</h1>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Every tool you need to work with PDFs, all in one place
          </p>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              href="/tools"
              className="inline-block bg-white text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-all shadow-lg"
            >
              View All Tools
            </Link>
          </motion.div>
        </motion.div>
      <ThemeToggle/>

      // </section> */}



    {/* Popular PDF Tools Section */}
<section className="py-20 container mx-auto px-4 md:px-6 bg-background transition-all duration-500 ease-in-out border-t border-blue-100 rounded-t-3xl shadow-inner">
  <motion.h2
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.2 }}
    className="text-4xl font-extrabold text-center mb-14 text-foreground tracking-tight"
  >
    Popular PDF Tools
  </motion.h2>

  <motion.div
    variants={container}
    initial="hidden"
    animate="show"
    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
  >
    {homepageTools.map((tool, index) => (
      <motion.div
        key={tool.id}
        variants={item}
      >
        <UnifiedCard
          id={tool.id}
          title={tool.title}
          description={tool.description}
          icon={tool.icon}
          type="tool"
          category={tool.category}
          inputFormat={tool.inputFormat}
          outputFormat={tool.outputFormat}
          popular={tool.popular}
          index={index}
          variant="default"
        />
      </motion.div>
    ))}
  </motion.div>

  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: 0.6 }}
    className="mt-12 text-center"
  >
    <Link
      href="/tools"
      className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-full font-medium hover:bg-primary/90 transition-all shadow-md group"
    >
      <span>See More Tools</span>
      <FiArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
    </Link>
  </motion.div>
</section>

{/* Top Calculators Section */}
<section className="py-20 container mx-auto px-4 md:px-6 bg-background transition-all duration-500 ease-in-out border-t border-blue-100 rounded-t-3xl shadow-inner">
  <motion.h2
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ delay: 0.2 }}
    className="text-4xl font-extrabold text-center mb-14 text-foreground tracking-tight"
  >
    Top Calculators
  </motion.h2>

  <motion.div
    variants={container}
    initial="hidden"
    animate="show"
    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
  >
    {homepageCalculators.map((calculator, index) => (
      <motion.div
        key={calculator.id}
        variants={item}
      >
        <UnifiedCard
          id={calculator.id}
          title={calculator.title}
          description={calculator.description}
          icon={calculator.icon}
          type="calculator"
          category={calculator.category}
          popular={calculator.popular}
          index={index}
          variant="default"
        />
      </motion.div>
    ))}
  </motion.div>

  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay: 0.6 }}
    className="mt-12 text-center"
  >
    <Link
      href="/calculators"
      className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-full font-medium hover:bg-primary/90 transition-all shadow-md group"
    >
      <span>See More Calculators</span>
      <FiArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
    </Link>
  </motion.div>
</section>

{/* Recent Blog Posts Section */}
<RecentBlogPosts limit={3} />

      <Footer />
    </main>
  );
}