'use client';

import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Calendar, User, ArrowRight, Heart, BookOpen } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import { getPinterestCategoryBadgeClasses, normalizePinterestCategoryName } from '@/lib/pinterestCategoryUtils';

interface BlogPost {
  id?: string;
  _id?: string;
  title: string;
  excerpt?: string;
  description?: string;
  content?: string;
  slug: string;
  featuredImage?: string;
  image?: string;
  imageCredit?: string;
  categoryId?: string;
  category?: string;
  categories?: string[];
  tags?: string[];
  publishedAt?: string;
  createdAt?: string;
  date?: string;
  author: {
    name: string;
    email?: string;
  } | string;
}

interface PinterestBlogCardProps {
  post: BlogPost;
  index?: number;
  showAnimation?: boolean;
  className?: string;
}

// Get category styling using Pinterest utilities
const getCategoryColor = (category: string = 'General') => {
  const normalizedCategory = normalizePinterestCategoryName(category);
  return getPinterestCategoryBadgeClasses(normalizedCategory);
};

// Utility functions
const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch {
    return 'Recent';
  }
};

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
};

const getReadingTime = (content: string = '') => {
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  const readingTime = Math.ceil(wordCount / wordsPerMinute);
  return readingTime < 1 ? 1 : readingTime;
};

export function PinterestBlogCard({
  post,
  index = 0,
  showAnimation = true,
  className = ""
}: PinterestBlogCardProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);
  const [isHovered, setIsHovered] = useState(false);

  // Normalize post data
  const normalizedPost = {
    id: post.id || post._id || '',
    title: post.title,
    excerpt: post.excerpt || post.description || '',
    content: post.content || '',
    slug: post.slug,
    featuredImage: post.featuredImage || post.image || '',
    imageCredit: post.imageCredit || '',
    category: post.category || (post.categories && post.categories[0]) || 'General',
    publishedAt: post.publishedAt || post.createdAt || post.date || new Date().toISOString(),
    author: typeof post.author === 'string' ? post.author : post.author?.name || 'Unknown Author'
  };

  const getImageSource = () => {
    if (imageError || !normalizedPost.featuredImage || normalizedPost.featuredImage.trim() === '') {
      // Generate a gradient placeholder based on category
      const gradients = [
        'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&q=80',
        'https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=800&q=80',
        'https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=800&q=80',
        'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&q=80',
        'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=800&q=80'
      ];
      return gradients[index % gradients.length];
    }
    return normalizedPost.featuredImage;
  };

  // Animation variants
  const cardVariants = showAnimation ? {
    hidden: { 
      opacity: 0, 
      y: 30,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        delay: index * 0.1,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  } : undefined;

  const hoverVariants = {
    scale: 1.02,
    y: -8,
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  };

  const imageVariants = {
    hover: {
      scale: 1.1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <Link href={`/blog/${normalizedPost.slug}`} className="block group cursor-pointer">
      <motion.div
        variants={cardVariants}
        initial={showAnimation ? "hidden" : false}
        animate={showAnimation ? "visible" : false}
        whileHover={hoverVariants}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        className={`relative ${className}`}
      >
        <div className="bg-card border border-border rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:border-primary/30 backdrop-blur-sm h-full flex flex-col">
          {/* Cover Image Section */}
          <div className="relative overflow-hidden bg-muted">
            {/* Dynamic height based on content */}
            <div className="relative" style={{ aspectRatio: '4/3' }}>
              {/* Loading State */}
              {imageLoading && (
                <div className="absolute inset-0 bg-muted animate-pulse flex items-center justify-center">
                  <div className="w-8 h-8 border-2 border-primary/30 border-t-primary rounded-full animate-spin" />
                </div>
              )}

              <motion.div
                variants={imageVariants}
                animate={isHovered ? "hover" : "initial"}
                className="relative w-full h-full"
              >
                <Image
                  src={getImageSource()}
                  alt={normalizedPost.title}
                  fill
                  className={`object-cover transition-all duration-700 ${
                    imageLoading ? 'opacity-0' : 'opacity-100'
                  }`}
                  onLoad={() => setImageLoading(false)}
                  onError={() => {
                    setImageError(true);
                    setImageLoading(false);
                  }}
                  priority={index < 6} // Prioritize first 6 images
                  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                />
              </motion.div>

              {/* Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

              {/* Category Badge */}
              <motion.div 
                className="absolute top-4 left-4 z-10"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 + 0.3 }}
              >
                <Badge className={`${getCategoryColor(normalizedPost.category)} text-xs font-bold border backdrop-blur-md shadow-lg`}>
                  {normalizedPost.category}
                </Badge>
              </motion.div>

              {/* Reading Time Badge */}
              <motion.div 
                className="absolute top-4 right-4 z-10"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 + 0.4 }}
              >
                <div className="bg-black/70 text-white text-xs px-3 py-1 rounded-full backdrop-blur-md flex items-center gap-1">
                  <BookOpen className="h-3 w-3" />
                  <span>{getReadingTime(normalizedPost.content)} min</span>
                </div>
              </motion.div>

              {/* Image Credit */}
              {normalizedPost.imageCredit && (
                <motion.div 
                  className="absolute bottom-3 right-3 z-10"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: isHovered ? 1 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <span className="text-xs text-white/90 bg-black/80 px-2 py-1 rounded-md backdrop-blur-sm">
                    📸 {normalizedPost.imageCredit}
                  </span>
                </motion.div>
              )}
            </div>
          </div>

          {/* Content Section */}
          <div className="p-6 flex-1 flex flex-col">
            {/* Publication Date */}
            <motion.div
              className="flex items-center gap-2 text-xs text-muted-foreground mb-3"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 + 0.5 }}
            >
              <Calendar className="h-3 w-3" />
              <span className="font-medium">{formatDate(normalizedPost.publishedAt)}</span>
            </motion.div>

            {/* Blog Title */}
            <motion.h3
              className="text-lg font-bold mb-3 line-clamp-2 text-foreground group-hover:text-primary transition-colors duration-300 leading-tight"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 + 0.6 }}
            >
              {normalizedPost.title}
            </motion.h3>

            {/* Excerpt */}
            {normalizedPost.excerpt && (
              <motion.p
                className="text-muted-foreground text-sm mb-4 line-clamp-3 leading-relaxed flex-1"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 + 0.7 }}
              >
                {truncateText(normalizedPost.excerpt, 100)}
              </motion.p>
            )}

            {/* Author and Read More */}
            <motion.div
              className="flex items-center justify-between pt-4 border-t border-border/50"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 + 0.8 }}
            >
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <User className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="text-sm font-medium text-foreground">{normalizedPost.author}</p>
                </div>
              </div>

              {/* Read More Arrow */}
              <motion.div
                className="flex items-center gap-2 text-primary group-hover:gap-3 transition-all duration-300"
                whileHover={{ x: 4 }}
              >
                <span className="text-sm font-semibold">Read</span>
                <ArrowRight className="h-4 w-4" />
              </motion.div>
            </motion.div>
          </div>
        </div>
      </motion.div>
    </Link>
  );
}
