import mongoose, { Schema, model, models, Types } from "mongoose";

export interface ICategory {
  _id: Types.ObjectId;
  name: string;
  description?: string;
  slug: string;
  count?: number;
  createdAt: Date;
  updatedAt: Date;
}

const categorySchema = new Schema<ICategory>(
  {
    name: {
      type: String,
      required: [true, "Category name is required"],
      trim: true,
      maxlength: [50, "Category name cannot exceed 50 characters"],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [200, "Description cannot exceed 200 characters"],
    },
    slug: {
      type: String,
      trim: true,
      lowercase: true,
      maxlength: [60, "Slug cannot exceed 60 characters"],
    },
    count: {
      type: Number,
      default: 0,
      min: [0, "Count cannot be negative"],
    },
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Create slug from name before saving
categorySchema.pre('save', function(next) {
  if (this.isModified('name')) {
    this.slug = this.name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w-]+/g, '')
      .substring(0, 60);
  }
  next();
});

// Index for better query performance
categorySchema.index({ name: 1 }, { unique: true });
categorySchema.index({ slug: 1 }, { unique: true });
categorySchema.index({ count: -1 });

// Virtual for formatted name
categorySchema.virtual('displayName').get(function() {
  return this.name.charAt(0).toUpperCase() + this.name.slice(1);
});

// Prevent model re-compilation during development
const Category = models.Category || model<ICategory>("Category", categorySchema);

export default Category;
