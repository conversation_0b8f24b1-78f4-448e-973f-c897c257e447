# Contact Management System Documentation

## 📋 Overview

The Contact Management System is a comprehensive solution for handling customer inquiries and support requests. It provides a complete admin interface for managing contact submissions with features like filtering, bulk operations, and real-time updates.

## ✅ Features Implemented

### 🗄️ Database & API
- **Complete Mongoose Model** (`src/models/Contact.ts`)
  - All required fields: name, email, category, message, status, priority
  - Optional fields: assignedTo, internalNotes, responseSent, ipAddress, userAgent
  - Soft delete support with `deletedAt` field
  - Text search indexing for efficient queries
  - Compound indexes for performance optimization

- **Full CRUD API Routes**
  - `GET /api/admin/contact` - List contacts with pagination and filtering
  - `POST /api/admin/contact` - Create new contact (admin only)
  - `GET /api/admin/contact/[id]` - Get single contact details
  - `PUT /api/admin/contact/[id]` - Update contact
  - `DELETE /api/admin/contact/[id]` - Delete contact (soft delete)
  - `POST /api/admin/contact/bulk` - Bulk operations
  - `GET /api/admin/contact/bulk/export` - CSV export

### 🎨 Admin Interface
- **Contact List Page** (`/admin/contact`)
  - Responsive table layout with all contact information
  - Real-time search with 300ms debouncing
  - Advanced filtering (status, category, priority, date range)
  - Sortable columns with visual indicators
  - Pagination with configurable page sizes (5, 10, 25, 50)
  - Bulk selection and operations

- **Contact Details Modal**
  - Complete contact information display
  - Formatted dates and status badges
  - Internal notes section
  - Assignment information

- **Contact Edit Modal**
  - Update status, priority, assignment
  - Add internal notes
  - Mark response as sent
  - Real-time admin user loading for assignments

### 🔧 Admin Sidebar Integration
- **Navigation Item** with unread count badge
- **Real-time Updates** - Fetches unread count every 30 seconds
- **Visual Indicator** - Red badge shows unread message count
- **Responsive Design** - Works on all screen sizes

### 📊 Statistics Dashboard
- **Real-time Stats Cards**
  - Total contacts
  - New (unread) contacts
  - Read contacts
  - In-progress contacts
  - Resolved contacts
  - High priority contacts
  - Contacts with responses sent

### 🔄 Bulk Operations
- **Multi-select Functionality**
  - Select individual contacts or all on page
  - Visual feedback for selected items
  - Bulk action toolbar appears when items selected

- **Supported Bulk Actions**
  - Mark as Read
  - Mark as Resolved
  - Delete multiple contacts
  - Assign to admin users
  - Update priority levels

### 📤 Export Functionality
- **CSV Export** with filtering
- **Customizable Columns**
- **Proper CSV Formatting** with escaped quotes
- **Filtered Export** - Respects current filter settings

## 🏗️ Architecture

### File Structure
```
src/
├── app/
│   ├── admin/contact/page.tsx          # Main contact management page
│   └── api/
│       ├── contact/route.ts            # Public contact form API
│       └── admin/contact/
│           ├── route.ts                # Admin contact list/create API
│           ├── [id]/route.ts           # Individual contact CRUD
│           └── bulk/route.ts           # Bulk operations & export
├── components/admin/
│   ├── AdminSidebar.tsx               # Sidebar with unread count
│   └── ContactDialogs.tsx             # Modal dialogs for contact management
├── models/Contact.ts                   # Mongoose schema and model
├── types/contact.ts                    # TypeScript interfaces
└── lib/contact-utils.ts               # Utility functions
```

### Database Schema
```typescript
interface IContact {
  _id: ObjectId;
  name: string;                    // Required, 2-100 chars
  email: string;                   // Required, valid email
  category: enum;                  // general|technical|bug|feature|business
  message: string;                 // Required, 10-5000 chars
  status: enum;                    // new|read|in-progress|resolved
  priority: enum;                  // low|medium|high
  assignedTo?: ObjectId;           // Reference to User model
  internalNotes?: string;          // Max 2000 chars
  responseSent: boolean;           // Default false
  ipAddress?: string;              // Client IP
  userAgent?: string;              // Client browser info
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;                // Soft delete
}
```

## 🔐 Security & Authentication

### Admin-Only Access
- All admin routes protected by middleware
- JWT token validation for admin role
- Proper error handling for unauthorized access

### Input Validation
- Zod schemas for all API endpoints
- Client-side form validation
- SQL injection prevention through Mongoose
- XSS protection through input sanitization

### Rate Limiting
- Contact form submission rate limiting
- Admin API rate limiting
- Proper error responses

## 🎯 Usage Guide

### For End Users
1. Visit `/contact` page
2. Fill out the contact form
3. Select appropriate category
4. Submit message
5. Receive confirmation

### For Administrators
1. Access admin panel at `/admin`
2. Navigate to "Contact Management"
3. View unread count in sidebar badge
4. Filter and search contacts
5. Click on contact to view details
6. Edit contact status and priority
7. Assign contacts to team members
8. Add internal notes
9. Mark responses as sent
10. Export data as CSV

### Bulk Operations
1. Select multiple contacts using checkboxes
2. Choose bulk action from toolbar
3. Confirm action in dialog
4. View updated results

## 🔧 Configuration

### Environment Variables
```env
MONGODB_URI=mongodb://localhost:27017/your-database
NEXTAUTH_SECRET=your-secret-key
```

### Customization Options
- **Categories**: Modify in `src/types/contact.ts`
- **Status Options**: Update in contact types
- **Priority Levels**: Customize in types file
- **Pagination Limits**: Adjust in admin page
- **Refresh Intervals**: Modify in sidebar component

## 🧪 Testing

### Manual Testing
1. Run the development server: `npm run dev`
2. Test contact form submission at `/contact`
3. Login as admin and test management features
4. Verify all CRUD operations work
5. Test bulk operations and export

### Automated Testing
```bash
# Run the test script
npx ts-node src/test-contact-system.ts
```

## 🚀 Performance Optimizations

### Database
- Compound indexes for efficient queries
- Text search indexes for full-text search
- Pagination to limit result sets
- Lean queries for better performance

### Frontend
- Debounced search to reduce API calls
- Optimistic UI updates
- Efficient re-rendering with React keys
- Lazy loading of admin users

### API
- Proper HTTP status codes
- Efficient MongoDB aggregation
- Bulk operations for multiple updates
- Streaming CSV export for large datasets

## 🔮 Future Enhancements

### Planned Features
1. **Real-time Updates** with WebSockets
2. **Email Integration** for automatic responses
3. **Contact Reply System** for two-way communication
4. **Advanced Analytics** with charts and graphs
5. **File Attachments** support
6. **Contact Templates** for common responses
7. **SLA Tracking** with response time metrics
8. **Integration APIs** for third-party tools

### Technical Improvements
1. **Unit Tests** with Jest and React Testing Library
2. **E2E Tests** with Playwright
3. **Performance Monitoring** with analytics
4. **Error Tracking** with Sentry
5. **API Documentation** with OpenAPI/Swagger
6. **Caching Layer** with Redis
7. **Background Jobs** for email processing

## 📞 Support

For technical support or questions about the contact management system:
- Check the code documentation in each file
- Review the TypeScript interfaces in `src/types/contact.ts`
- Examine the API routes for endpoint details
- Test with the provided test script

## 📄 License

This contact management system is part of the ToolBox application and follows the same licensing terms.
