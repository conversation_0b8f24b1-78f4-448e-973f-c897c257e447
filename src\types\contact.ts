// types/contact.ts - Centralized contact management types

export interface Contact {
  _id: string;
  name: string;
  email: string;
  category: "general" | "technical" | "bug" | "feature" | "business";
  message: string;
  status: "new" | "read" | "in-progress" | "resolved";
  priority: "low" | "medium" | "high";
  assignedTo?: {
    _id: string;
    name: string;
    email: string;
  };
  internalNotes?: string;
  responseSent: boolean;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
}

export interface ContactStats {
  total: number;
  new: number;
  read: number;
  inProgress: number;
  resolved: number;
  highPriority: number;
  responseSent: number;
}

export interface ContactPagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  limit: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface ContactFilters {
  status: "new" | "read" | "in-progress" | "resolved" | "all";
  category: "general" | "technical" | "bug" | "feature" | "business" | "all";
  priority: "low" | "medium" | "high" | "all";
  assignedTo: string;
  dateRange: "7" | "30" | "90" | "all";
  search?: string;
}

export interface ContactResponse {
  success: boolean;
  data: {
    contacts: Contact[];
    pagination: ContactPagination;
    stats: ContactStats;
    filters: ContactFilters;
  };
  message?: string;
  error?: string;
}

export interface AdminUser {
  _id: string;
  name: string;
  email: string;
  role: "admin";
}

export interface ContactFormData {
  name: string;
  email: string;
  category: "general" | "technical" | "bug" | "feature" | "business";
  message: string;
}

export interface ContactUpdateData {
  status?: "new" | "read" | "in-progress" | "resolved";
  priority?: "low" | "medium" | "high";
  assignedTo?: string | null;
  internalNotes?: string;
  responseSent?: boolean;
}

export interface BulkContactAction {
  action: "updateStatus" | "updatePriority" | "assign" | "delete" | "markResponseSent";
  contactIds: string[];
  data?: ContactUpdateData;
}

// Dialog state interfaces
export interface ContactViewDialog {
  open: boolean;
  contact: Contact | null;
}

export interface ContactEditDialog {
  open: boolean;
  contact: Contact | null;
}

export interface ContactDeleteDialog {
  open: boolean;
  contactId: string | null;
}

export interface ContactBulkDialog {
  open: boolean;
  action: string | null;
}

// Category configuration
export const CONTACT_CATEGORIES = [
  { value: "general", label: "General Inquiry" },
  { value: "technical", label: "Technical Support" },
  { value: "bug", label: "Bug Report" },
  { value: "feature", label: "Feature Request" },
  { value: "business", label: "Business Partnership" },
] as const;

// Status configuration
export const CONTACT_STATUSES = [
  { value: "new", label: "New", color: "blue" },
  { value: "read", label: "Read", color: "gray" },
  { value: "in-progress", label: "In Progress", color: "yellow" },
  { value: "resolved", label: "Resolved", color: "green" },
] as const;

// Priority configuration
export const CONTACT_PRIORITIES = [
  { value: "low", label: "Low", color: "gray" },
  { value: "medium", label: "Medium", color: "blue" },
  { value: "high", label: "High", color: "red" },
] as const;
