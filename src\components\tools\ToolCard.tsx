"use client"

import React from "react";
import Link from "next/link";
import { <PERSON><PERSON>rrowR<PERSON>, <PERSON>H<PERSON>t, FiShare2, FiMoreVertical } from "react-icons/fi";
import { motion } from "framer-motion";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { TouchableCard } from "@/components/ui/TouchableCard";
import { hapticFeedback } from "@/hooks/useTouch";

interface ToolCardProps {
  title: string;
  description: string;
  icon: string;
  href: string;
  inputFormat?: string;
  outputFormat?: string;
  category: string;
  delay?: number;
  id?: string; // Add id for consistent routing
  // componentName:string;
}

const ToolCard = React.memo(function ToolCard({
  title,
  description,
  icon,
  href,
  inputFormat,
  outputFormat,
  category,
  id,
  // componentName,
  delay = 0,
}: ToolCardProps) {
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);
  const [isFavorited, setIsFavorited] = useState(false);
  const [showOptions, setShowOptions] = useState(false);

  const categoryColors = {
    pdf: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
    office: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
    image: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
    web: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'
  };

  // Always use id-based routing for consistency
  const finalHref = id ? `/tools/${id}` : (href || `/tools/${title.toLowerCase().replace(/\s+/g, '-')}`);

  const handleTap = () => {
    setIsNavigating(true);
    try {
      router.push(finalHref);
    } catch (error) {
      console.error('Navigation error:', error);
      window.location.href = finalHref;
    }
    setTimeout(() => setIsNavigating(false), 1000);
  };

  const handleLongPress = () => {
    setShowOptions(true);
    setTimeout(() => setShowOptions(false), 3000);
  };

  const handleSwipeLeft = () => {
    setIsFavorited(!isFavorited);
    hapticFeedback.success();
  };

  const handleSwipeRight = () => {
    if (navigator.share) {
      navigator.share({
        title: title,
        text: description,
        url: finalHref,
      });
    }
    hapticFeedback.medium();
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay }}
      className="h-full"
    >
      <TouchableCard
        onTap={handleTap}
        onLongPress={handleLongPress}
        onSwipeLeft={handleSwipeLeft}
        onSwipeRight={handleSwipeRight}
        className={`group relative h-full p-6 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 transition-all shadow-sm overflow-hidden ${
          isNavigating ? 'opacity-75' : ''
        }`}
        variant="elevated"
        size="md"
        cardType="tool"
        enableHoverAnimations={true}
      >
        <div className="flex items-start justify-between mb-4">
          <span className="tool-icon text-3xl transition-transform duration-300" aria-hidden="true">
            {icon}
          </span>
          <div className="flex items-center gap-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${categoryColors[category as keyof typeof categoryColors]}`}>
              {category}
            </span>
            {isFavorited && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="text-red-500"
              >
                <FiHeart className="w-4 h-4 fill-current" />
              </motion.div>
            )}
          </div>
        </div>

        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
          {title}
        </h3>
        <p className="text-gray-600 dark:text-gray-300 mb-4">{description}</p>

        <div className="flex justify-between items-center mt-auto">
          {inputFormat && outputFormat && (
            <div className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
              {inputFormat} → {outputFormat}
            </div>
          )}
          <motion.div
            whileHover={{ x: 4 }}
            transition={{ duration: 0.2 }}
            className="ml-auto"
          >
            <FiArrowRight className="hover-arrow w-5 h-5 text-gray-400 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-all" />
          </motion.div>
        </div>

        <div className="absolute inset-0 -z-10 bg-gradient-to-br from-blue-50/50 to-white/50 dark:from-blue-900/20 dark:to-gray-800/50 opacity-0 group-hover:opacity-100 transition-opacity" />

        {/* Touch Options Overlay */}
        {showOptions && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute inset-0 bg-black/50 flex items-center justify-center gap-4 rounded-xl"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="flex items-center gap-2 bg-blue-500 text-white px-3 py-2 rounded-full text-sm"
            >
              <FiHeart className="w-4 h-4" />
              Swipe left to favorite
            </motion.div>
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="flex items-center gap-2 bg-green-500 text-white px-3 py-2 rounded-full text-sm"
            >
              <FiShare2 className="w-4 h-4" />
              Swipe right to share
            </motion.div>
          </motion.div>
        )}
      </TouchableCard>
    </motion.div>
  );
});

export default ToolCard;