# Contact Management System & Google OAuth Setup

This document provides comprehensive setup instructions for the new Contact Management System and Google OAuth authentication features.

## 🚀 Features Implemented

### Contact Management System
- **Admin Panel Interface** at `/admin/contact`
- **Real-time Dashboard** with contact statistics
- **Advanced Filtering & Search** with debounced input
- **Bulk Operations** for managing multiple contacts
- **Status Management** (new, read, in-progress, resolved)
- **Priority Levels** (low, medium, high)
- **Assignment System** for admin users
- **Internal Notes** for admin-only communication
- **CSV Export** functionality
- **Real-time Notifications** with unread count badge

### Google OAuth Integration
- **Google Sign-in** button on login page
- **Automatic Account Creation** for new Google users
- **Account Linking** for existing users
- **Seamless Authentication** alongside existing credentials

### Database Schema
- **Contact Model** with comprehensive fields
- **MongoDB Indexes** for optimal performance
- **Soft Delete** functionality
- **Audit Trail** with timestamps and user tracking

## 📋 Prerequisites

- Node.js 18+ and npm
- MongoDB database
- Google Cloud Console account
- NextAuth.js configured

## 🔧 Setup Instructions

### 1. Google OAuth Configuration

#### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API

#### Step 2: Create OAuth 2.0 Credentials
1. Navigate to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth 2.0 Client IDs**
3. Configure the consent screen if prompted
4. Set application type to **Web application**
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://yourdomain.com/api/auth/callback/google` (production)

#### Step 3: Configure Environment Variables
Add to your `.env.local` file:

```env
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
```

### 2. Database Setup

The Contact model will be automatically created when you first run the application. The following indexes are created for optimal performance:

- Email index
- Status + CreatedAt compound index
- Category + Status compound index
- AssignedTo + Status compound index
- Text search index (name, email, message, internal notes)

### 3. Admin Access

Ensure your user account has admin role to access the contact management system:

1. Log in to your application
2. Update your user role in MongoDB:
```javascript
db.users.updateOne(
  { email: "<EMAIL>" },
  { $set: { role: "admin" } }
)
```

## 🎯 Usage Guide

### Contact Management Dashboard

#### Accessing the Dashboard
- Navigate to `/admin/contact` (admin role required)
- View real-time statistics and contact overview

#### Key Features

**Statistics Cards:**
- Total contacts
- New contacts (unread)
- Read contacts
- In-progress contacts
- Resolved contacts
- High priority contacts
- Contacts with responses sent

**Filtering & Search:**
- Real-time search across name, email, and message content
- Filter by status, category, priority, date range
- Sort by any column (name, email, status, priority, date)

**Bulk Operations:**
- Select multiple contacts using checkboxes
- Mark as read/resolved in bulk
- Delete multiple contacts with confirmation

**Individual Contact Actions:**
- View full contact details
- Edit status, priority, assignment, and internal notes
- Delete individual contacts
- Track response status

#### Contact Workflow

1. **New Contact Submission**
   - Automatically created with "new" status
   - Appears in admin dashboard immediately
   - Notification badge shows unread count

2. **Contact Review**
   - Admin views contact details
   - Status changes to "read" when opened
   - Can assign to specific admin user

3. **Processing**
   - Update status to "in-progress"
   - Add internal notes for team communication
   - Set priority level (low/medium/high)

4. **Resolution**
   - Mark as "resolved" when completed
   - Track if response was sent to customer
   - Export data for reporting

### Google OAuth Login

#### For Users
1. Visit `/login` page
2. Click "Sign in with Google" button
3. Authorize the application
4. Automatic account creation or linking

#### For Admins
- Google OAuth users get "user" role by default
- Admin role must be assigned manually in database
- Existing admin accounts can link Google OAuth

## 🔒 Security Features

### Authentication
- NextAuth.js with JWT tokens
- Secure session management
- Role-based access control

### Data Protection
- Input validation with Zod schemas
- SQL injection prevention
- XSS protection
- CSRF protection via NextAuth

### Admin Security
- Admin-only routes protected by middleware
- Contact management requires admin role
- Internal notes not visible to contact submitters

## 📊 API Endpoints

### Contact Management APIs

#### GET `/api/admin/contact`
- List contacts with pagination and filtering
- Query parameters: page, limit, search, status, category, priority, dateRange, sortBy, sortOrder

#### GET `/api/admin/contact/[id]`
- Get single contact details
- Requires admin authentication

#### PUT `/api/admin/contact/[id]`
- Update contact status, priority, assignment, notes
- Requires admin authentication

#### DELETE `/api/admin/contact/[id]`
- Soft delete contact
- Requires admin authentication

#### POST `/api/admin/contact/bulk`
- Bulk operations on multiple contacts
- Actions: updateStatus, updatePriority, assign, delete, markResponseSent

#### GET `/api/admin/contact/bulk/export`
- Export contacts as CSV
- Supports filtering parameters

### Public Contact API

#### POST `/api/contact`
- Submit new contact form
- Public endpoint with rate limiting
- Automatic validation and storage

## 🎨 UI Components

### Admin Dashboard
- **Responsive Design** - Works on desktop, tablet, and mobile
- **Dark/Light Mode** - Follows system theme preference
- **Real-time Updates** - Auto-refresh every 30 seconds
- **Loading States** - Smooth loading indicators
- **Error Handling** - User-friendly error messages

### Contact Dialogs
- **View Dialog** - Full contact details with formatted display
- **Edit Dialog** - Update contact information with form validation
- **Delete Dialog** - Confirmation dialog for destructive actions
- **Bulk Dialog** - Confirmation for bulk operations

### Navigation
- **Sidebar Badge** - Shows unread contact count
- **Auto-refresh** - Updates count every 30 seconds
- **Visual Indicators** - Color-coded status and priority badges

## 🚨 Troubleshooting

### Common Issues

#### Google OAuth Not Working
1. Check environment variables are set correctly
2. Verify redirect URIs in Google Console
3. Ensure Google+ API is enabled
4. Check browser console for errors

#### Contact Dashboard Not Loading
1. Verify admin role in database
2. Check MongoDB connection
3. Ensure Contact model is properly created
4. Check browser network tab for API errors

#### Real-time Updates Not Working
1. Check if polling is enabled (30-second intervals)
2. Verify API endpoints are accessible
3. Check browser console for JavaScript errors

### Database Issues

#### Missing Indexes
Run this MongoDB command to create indexes manually:
```javascript
db.contacts.createIndex({ email: 1 })
db.contacts.createIndex({ status: 1, createdAt: -1 })
db.contacts.createIndex({ category: 1, status: 1 })
db.contacts.createIndex({ 
  name: "text", 
  email: "text", 
  message: "text",
  internalNotes: "text"
})
```

#### Performance Issues
- Monitor database query performance
- Consider adding more specific indexes
- Implement pagination for large datasets
- Use aggregation pipelines for complex queries

## 📈 Performance Optimization

### Frontend
- **Debounced Search** - 300ms delay to reduce API calls
- **Optimistic Updates** - Immediate UI updates with rollback on error
- **Pagination** - Configurable page sizes (5, 10, 25, 50)
- **Lazy Loading** - Components load on demand

### Backend
- **Database Indexes** - Optimized for common queries
- **Aggregation Pipelines** - Efficient statistics calculation
- **Caching** - Consider Redis for frequently accessed data
- **Rate Limiting** - Prevent API abuse

### Real-time Features
- **Polling Strategy** - 30-second intervals for admin dashboard
- **WebSocket Ready** - Architecture supports WebSocket upgrade
- **Efficient Queries** - Only fetch necessary data for updates

## 🔄 Future Enhancements

### Planned Features
- **WebSocket Integration** - Real-time updates without polling
- **Email Integration** - Send responses directly from admin panel
- **Template System** - Pre-defined response templates
- **Advanced Analytics** - Response time tracking, satisfaction metrics
- **Mobile App** - React Native admin app
- **Automation** - Auto-assignment based on category/priority

### Integration Opportunities
- **CRM Systems** - Salesforce, HubSpot integration
- **Help Desk** - Zendesk, Freshdesk integration
- **Slack/Discord** - Notification webhooks
- **Email Services** - SendGrid, Mailgun integration

## 📞 Support

For technical support or questions about the Contact Management System:

1. Check this documentation first
2. Review the troubleshooting section
3. Check browser console and network tabs for errors
4. Verify database connectivity and permissions
5. Contact the development team with specific error messages

---

**Note:** This system is designed to scale with your business needs. Regular monitoring and maintenance will ensure optimal performance as your contact volume grows.
