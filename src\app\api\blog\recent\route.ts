import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import BlogPost from "@/models/BlogPost";
import "@/models/User"; // Import User model for population
import "@/models/Category"; // Import Category model for population

// Simple in-memory cache for recent posts
let recentPostsCache: any = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "3");
    const cacheKey = `recent_posts_${limit}`;

    // Check cache first
    const now = Date.now();
    if (recentPostsCache && recentPostsCache[cacheKey] && (now - cacheTimestamp) < CACHE_DURATION) {
      return NextResponse.json({
        success: true,
        data: recentPostsCache[cacheKey],
        count: recentPostsCache[cacheKey].length,
        cached: true
      });
    }

    await connectToDatabase();

    // Fetch recent published posts with optimized query
    const posts = await BlogPost.find({
      status: "published",
      visibility: "public"
    })
      .select("title slug description featuredImage publishedAt createdAt authorId categoryId categoryName credit")
      .sort({ publishedAt: -1, createdAt: -1 })
      .limit(limit)
      .populate("authorId", "name email")
      .populate("categoryId", "name slug")
      .lean(); // Use lean() for better performance

    // Format posts for frontend
    const formattedPosts = posts.map(post => ({
      id: post._id.toString(),
      title: post.title,
      slug: post.slug,
      excerpt: post.description || post.title,
      featuredImage: post.featuredImage || "/images/blog-placeholder.jpg",
      image: post.featuredImage || "/images/blog-placeholder.jpg", // Keep both for compatibility
      publishedAt: post.publishedAt || post.createdAt,
      categoryId: post.categoryId?._id || post.categoryId || null,
      category: post.categoryId?.name || "General",
      author: {
        name: post.authorId?.name || "Anonymous",
        email: post.authorId?.email || ""
      }
    }));

    // Update cache
    if (!recentPostsCache) recentPostsCache = {};
    recentPostsCache[cacheKey] = formattedPosts;
    cacheTimestamp = now;

    return NextResponse.json({
      success: true,
      data: formattedPosts,
      count: formattedPosts.length
    });

  } catch (error) {
    console.error("GET /api/blog/recent error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch recent posts",
        data: [],
        count: 0
      },
      { status: 500 }
    );
  }
}
