# ToolBox Project Cleanup Script
# Safely removes test files, development artifacts, and unused components
# Generated: 2025-06-29

param(
    [switch]$DryRun = $false,
    [switch]$Verbose = $false,
    [switch]$SkipConfirmation = $false
)

# Color output functions
function Write-Success { param($Message) Write-Host $Message -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host $Message -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host $Message -ForegroundColor Red }
function Write-Info { param($Message) Write-Host $Message -ForegroundColor Cyan }

# Initialize counters
$FilesRemoved = 0
$DirectoriesRemoved = 0
$TotalSizeFreed = 0

Write-Info "🧹 ToolBox Project Cleanup Script"
Write-Info "=================================="

if ($DryRun) {
    Write-Warning "🔍 DRY RUN MODE - No files will be deleted"
}

# Function to safely remove files/directories
function Remove-SafelyWithConfirmation {
    param(
        [string]$Path,
        [string]$Description,
        [string]$Category = "File"
    )
    
    if (-not (Test-Path $Path)) {
        if ($Verbose) { Write-Warning "⚠️  Path not found: $Path" }
        return
    }
    
    $item = Get-Item $Path
    $size = 0
    
    if ($item.PSIsContainer) {
        $size = (Get-ChildItem $Path -Recurse -File | Measure-Object -Property Length -Sum).Sum
        $Category = "Directory"
    } else {
        $size = $item.Length
    }
    
    $sizeKB = [math]::Round($size / 1KB, 2)
    
    Write-Info "📁 $Category`: $Description"
    Write-Info "   Path: $Path"
    Write-Info "   Size: $sizeKB KB"
    
    if ($DryRun) {
        Write-Warning "   [DRY RUN] Would remove this $($Category.ToLower())"
        return
    }
    
    if (-not $SkipConfirmation) {
        $confirmation = Read-Host "   Remove this $($Category.ToLower())? (y/N)"
        if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
            Write-Warning "   Skipped by user"
            return
        }
    }
    
    try {
        if ($item.PSIsContainer) {
            Remove-Item $Path -Recurse -Force
            $script:DirectoriesRemoved++
        } else {
            Remove-Item $Path -Force
            $script:FilesRemoved++
        }
        $script:TotalSizeFreed += $size
        Write-Success "   ✅ Removed successfully"
    }
    catch {
        Write-Error "   ❌ Failed to remove: $($_.Exception.Message)"
    }
}

Write-Info "`n🔴 HIGH PRIORITY CLEANUP - Test Files & Development Artifacts"
Write-Info "=============================================================="

# Test directories
$testDirectories = @(
    @{ Path = "src/app/test"; Description = "Complete test directory with all test pages" },
    @{ Path = "src/app/test-hover"; Description = "Hover animation test page" },
    @{ Path = "src/app/test-touch"; Description = "Touch interaction test page" },
    @{ Path = "src/app/test-blog-api"; Description = "Blog API testing page" },
    @{ Path = "src/app/test-category"; Description = "Category testing page" },
    @{ Path = "src/app/test-cloudinary"; Description = "Cloudinary integration tests" },
    @{ Path = "src/app/test-minimal"; Description = "Minimal test page" },
    @{ Path = "src/app/test-navigation"; Description = "Navigation testing page" },
    @{ Path = "src/app/test-unified-routing"; Description = "Unified routing test page" },
    @{ Path = "src/components/testing"; Description = "Test components directory" }
)

foreach ($dir in $testDirectories) {
    Remove-SafelyWithConfirmation -Path $dir.Path -Description $dir.Description -Category "Directory"
}

# Test files
$testFiles = @(
    @{ Path = "src/test-contact-system.ts"; Description = "Contact system test script" },
    @{ Path = "test-contact-system.js"; Description = "Duplicate contact test file" },
    @{ Path = "hover-test.html"; Description = "Static hover test file" },
    @{ Path = "test-blog-editor.md"; Description = "Blog editor test documentation" }
)

foreach ($file in $testFiles) {
    Remove-SafelyWithConfirmation -Path $file.Path -Description $file.Description
}

# Development artifacts
$devArtifacts = @(
    @{ Path = "src/lib/tempo-mock.ts"; Description = "Mock Tempo devtools implementation" },
    @{ Path = "src/app/blog-demo"; Description = "Blog demo page directory" },
    @{ Path = "src/app/home-redesign"; Description = "Home redesign experiments" }
)

foreach ($artifact in $devArtifacts) {
    $category = if (Test-Path $artifact.Path -PathType Container) { "Directory" } else { "File" }
    Remove-SafelyWithConfirmation -Path $artifact.Path -Description $artifact.Description -Category $category
}

Write-Info "`n🟡 MEDIUM PRIORITY CLEANUP - Documentation & Legacy Files"
Write-Info "=========================================================="

# Historical documentation (keep essential ones)
$historicalDocs = @(
    @{ Path = "BLOG_COMPONENTS_FIXES.md"; Description = "Historical blog component fixes" },
    @{ Path = "CATEGORY_DROPDOWN_FIXES.md"; Description = "Historical category dropdown fixes" },
    @{ Path = "CATEGORY_DROPDOWN_IMPLEMENTATION.md"; Description = "Historical implementation docs" },
    @{ Path = "CONTACT_SYSTEM_FIXES.md"; Description = "Historical contact system fixes" },
    @{ Path = "CONTACT_SYSTEM_VERIFICATION.md"; Description = "Historical verification docs" },
    @{ Path = "HOVER_ANIMATIONS_FIX.md"; Description = "Historical hover animation fixes" },
    @{ Path = "blog-editor-fixes-summary.md"; Description = "Historical blog editor fixes" },
    @{ Path = "final-blog-editor-status.md"; Description = "Historical blog editor status" }
)

Write-Warning "📚 The following documentation files contain historical fix information."
Write-Warning "Consider keeping PERFORMANCE_FIXES_SUMMARY.md, TOUCH_IMPLEMENTATION.md, and AUTH_SETUP.md"

foreach ($doc in $historicalDocs) {
    Remove-SafelyWithConfirmation -Path $doc.Path -Description $doc.Description
}

Write-Info "`n🟢 LOW PRIORITY CLEANUP - Optional Consolidation"
Write-Info "================================================"

# Duplicate context files
if ((Test-Path "src/context/theme-context.tsx") -and (Test-Path "src/contexts/ThemeContext.tsx")) {
    Write-Warning "🔄 Duplicate theme context files detected:"
    Write-Warning "   - src/context/theme-context.tsx"
    Write-Warning "   - src/contexts/ThemeContext.tsx"
    Write-Warning "   Manual review recommended to consolidate these files"
}

# Check for unused type definition files
$typeFiles = @(
    @{ Path = "src/types/components.d.ts"; Description = "Generic component types (consider consolidating)" },
    @{ Path = "src/types/css.d.ts"; Description = "CSS type definitions (verify if needed)" },
    @{ Path = "src/types/type.d.ts"; Description = "Generic type file (consider renaming/consolidating)" }
)

Write-Warning "📝 Type definition files found that may need consolidation:"
foreach ($typeFile in $typeFiles) {
    if (Test-Path $typeFile.Path) {
        Write-Warning "   - $($typeFile.Path): $($typeFile.Description)"
    }
}

# Summary
Write-Info "`n📊 CLEANUP SUMMARY"
Write-Info "=================="

if ($DryRun) {
    Write-Info "🔍 DRY RUN COMPLETED - No files were actually removed"
} else {
    Write-Success "✅ Files removed: $FilesRemoved"
    Write-Success "✅ Directories removed: $DirectoriesRemoved"
    $sizeMB = [math]::Round($TotalSizeFreed / 1MB, 2)
    Write-Success "✅ Total space freed: $sizeMB MB"
}

Write-Info "`n🔧 NEXT STEPS"
Write-Info "============="
Write-Info "1. Run 'npm run build' to verify everything still works"
Write-Info "2. Test key functionality (blog, tools, calculators, admin)"
Write-Info "3. Commit changes with descriptive message"
Write-Info "4. Consider implementing recommended security enhancements"

Write-Info "`n🎯 RECOMMENDED FOLLOW-UP ACTIONS"
Write-Info "================================"
Write-Info "• Implement Content Security Policy (CSP) headers"
Write-Info "• Add webpack-bundle-analyzer for bundle optimization"
Write-Info "• Set up automated testing to replace manual test files"
Write-Info "• Consider implementing PWA features for offline functionality"

if (-not $DryRun) {
    Write-Success "`n🎉 Cleanup completed successfully!"
    Write-Info "Your ToolBox project is now cleaner and more maintainable."
}
