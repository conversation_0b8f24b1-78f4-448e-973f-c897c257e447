import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { z } from "zod";
import connectToDatabase from "@/lib/db";
import Contact from "@/models/Contact";
import mongoose from "mongoose";

const secret = process.env.NEXTAUTH_SECRET;

// Validation schema for contact updates
const ContactUpdateSchema = z.object({
  status: z.enum(["new", "read", "in-progress", "resolved"]).optional(),
  priority: z.enum(["low", "medium", "high"]).optional(),
  assignedTo: z.string().optional().nullable(),
  internalNotes: z.string().max(2000).optional(),
  responseSent: z.boolean().optional(),
});

// Helper function to check admin authorization
async function checkAdminAuth(request: NextRequest) {
  const token = await getToken({ req: request, secret });
  
  if (!token || token.role !== "admin") {
    return NextResponse.json(
      { error: "Unauthorized. Admin access required." },
      { status: 401 }
    );
  }
  
  return null;
}

// Helper function to validate ObjectId
function isValidObjectId(id: string): boolean {
  return mongoose.Types.ObjectId.isValid(id);
}

// GET /api/admin/contact/[id] - Get single contact
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authorization
    const authError = await checkAdminAuth(request);
    if (authError) return authError;

    const { id } = await params;

    if (!isValidObjectId(id)) {
      return NextResponse.json(
        { error: "Invalid contact ID" },
        { status: 400 }
      );
    }

    await connectToDatabase();

    const contact = await Contact.findOne({ 
      _id: id, 
      deletedAt: { $exists: false } 
    }).populate("assignedTo", "name email");

    if (!contact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: contact,
    });

  } catch (error) {
    console.error("Error fetching contact:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/contact/[id] - Update contact
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authorization
    const authError = await checkAdminAuth(request);
    if (authError) return authError;

    const { id } = await params;

    if (!isValidObjectId(id)) {
      return NextResponse.json(
        { error: "Invalid contact ID" },
        { status: 400 }
      );
    }

    await connectToDatabase();

    const body = await request.json();
    const validation = ContactUpdateSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: "Validation failed", details: validation.error.issues },
        { status: 400 }
      );
    }

    const updateData = validation.data;

    // Handle assignedTo field - convert empty string to null
    if (updateData.assignedTo === "") {
      updateData.assignedTo = null;
    }

    // Validate assignedTo ObjectId if provided
    if (updateData.assignedTo && !isValidObjectId(updateData.assignedTo)) {
      return NextResponse.json(
        { error: "Invalid assignedTo user ID" },
        { status: 400 }
      );
    }

    const contact = await Contact.findOneAndUpdate(
      { _id: id, deletedAt: { $exists: false } },
      updateData,
      { new: true, runValidators: true }
    ).populate("assignedTo", "name email");

    if (!contact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: contact,
      message: "Contact updated successfully",
    });

  } catch (error) {
    console.error("Error updating contact:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/contact/[id] - Soft delete contact
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check admin authorization
    const authError = await checkAdminAuth(request);
    if (authError) return authError;

    const { id } = await params;

    if (!isValidObjectId(id)) {
      return NextResponse.json(
        { error: "Invalid contact ID" },
        { status: 400 }
      );
    }

    await connectToDatabase();

    const contact = await Contact.findOneAndUpdate(
      { _id: id, deletedAt: { $exists: false } },
      { deletedAt: new Date() },
      { new: true }
    );

    if (!contact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Contact deleted successfully",
    });

  } catch (error) {
    console.error("Error deleting contact:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
