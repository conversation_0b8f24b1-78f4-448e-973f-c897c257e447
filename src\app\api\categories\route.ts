import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import Category from "@/models/Category";
import { z } from "zod";

// Schema validation for categories
const CategorySchema = z.object({
  name: z.string().min(1, "Category name is required"),
  description: z.string().optional(),
});

// GET all categories
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    // Import BlogPost model for counting
    const BlogPost = (await import("@/models/BlogPost")).default;

    // Get categories and counts in parallel for better performance
    const [categories, postCounts] = await Promise.all([
      Category.find().sort({ name: 1 }).lean(),
      BlogPost.aggregate([
        {
          $match: {
            status: "published",
            visibility: "public",
            categoryId: { $exists: true, $ne: null }
          }
        },
        {
          $group: {
            _id: "$categoryId",
            count: { $sum: 1 }
          }
        }
      ])
    ]);

    // Create a map for quick lookup
    const countMap = postCounts.reduce((acc, item) => {
      acc[item._id.toString()] = item.count;
      return acc;
    }, {} as Record<string, number>);

    // Combine categories with their counts
    const categoriesWithCounts = categories.map(category => ({
      ...category,
      count: countMap[category._id.toString()] || 0
    }));

    return NextResponse.json({
      success: true,
      data: categoriesWithCounts,
      count: categoriesWithCounts.length
    });
  } catch (error: any) {
    console.error("Error fetching categories:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch categories",
        data: [],
        count: 0
      },
      { status: 500 }
    );
  }
}

// POST a new category
export async function POST(request: NextRequest) {
  try {
    // Check authentication using request headers (set by middleware)
    const userRole = request.headers.get("x-user-role");

    // Only admin users can create categories
    if (userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized - Admin access required" },
        { status: 401 }
      );
    }

    await connectToDatabase();

    const body = await request.json();
    const validation = CategorySchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: "Validation failed", details: validation.error },
        { status: 400 }
      );
    }

    // Check if category already exists (case-insensitive)
    const existingCategory = await Category.findOne({
      name: { $regex: new RegExp(`^${validation.data.name.trim()}$`, 'i') }
    });

    if (existingCategory) {
      return NextResponse.json(
        {
          error: "Category already exists",
          existingCategory: {
            _id: existingCategory._id,
            name: existingCategory.name,
            slug: existingCategory.slug
          }
        },
        { status: 409 }
      );
    }

    // Create new category
    const newCategory = await Category.create(validation.data);

    return NextResponse.json(newCategory, { status: 201 });
  } catch (error: any) {
    console.error("Error creating category:", error);
    return NextResponse.json(
      { error: "Failed to create category" },
      { status: 500 }
    );
  }
}
