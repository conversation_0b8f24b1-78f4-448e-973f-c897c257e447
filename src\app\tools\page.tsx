'use client';

import Link from "next/link";
import { useTheme } from "@/hooks/useTheme";
import { ALL_TOOLS } from "@/data/tools";
import ToolsHeader from "@/components/tools/ToolsHeader";
import UnifiedCard from "@/components/ui/UnifiedCard";
import BackButton from "@/components/ui/BackButton";
import RecentBlogPosts from "@/components/home/<USER>";
import Footer from "@/components/layout/Footer";
import { motion } from "framer-motion";
import { FiArrowRight, FiSearch, FiTool, FiFileText, FiDownload, FiGrid, FiLayers } from "react-icons/fi";
import { useState, useEffect, useMemo } from "react";
import { STATIC_ROUTES } from "@/lib/routing";
import * as LucideIcons from "lucide-react";

export default function ToolsPage() {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredTools, setFilteredTools] = useState<any[]>([]);
  const [filteredComingSoonTools, setFilteredComingSoonTools] = useState<any[]>([]);
  const [totalToolCount, setTotalToolCount] = useState(0);

  // We don't need to manually apply the theme here anymore
  // The useTheme hook now handles this globally

  // Memoize tool separation for better performance
  const { availableTools, comingSoonTools } = useMemo(() => {
    const available = ALL_TOOLS.filter(tool => tool.hasConfig !== false);
    const upcoming = ALL_TOOLS.filter(tool => tool.hasConfig === false);
    return { availableTools: available, comingSoonTools: upcoming };
  }, []);

  // Initialize filtered tools and calculate total tool count
  useEffect(() => {
    // Initialize filtered tools with all tools
    setFilteredTools(availableTools);
    setFilteredComingSoonTools(comingSoonTools);

    // Set the total count of all tools (available + coming soon)
    setTotalToolCount(availableTools.length + comingSoonTools.length);
  }, [availableTools, comingSoonTools]);

  // Filter tools based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredTools(availableTools);
      setFilteredComingSoonTools(comingSoonTools);
    } else {
      const query = searchQuery.toLowerCase().trim();

      // Filter available tools
      const filtered = availableTools.filter(tool =>
        tool.title.toLowerCase().includes(query) ||
        tool.description.toLowerCase().includes(query)
      );
      setFilteredTools(filtered);

      // Filter coming soon tools
      const filteredComingSoon = comingSoonTools.filter(tool =>
        tool.title.toLowerCase().includes(query) ||
        tool.description.toLowerCase().includes(query)
      );
      setFilteredComingSoonTools(filteredComingSoon);
    }
  }, [searchQuery, availableTools, comingSoonTools]);

  return (
    <main className="min-h-screen bg-var-bg-primary text-var-text-primary transition-colors duration-300" style={{
      backgroundColor: 'var(--bg-primary)',
      color: 'var(--text-primary)'
    }}>
      <ToolsHeader />

      {/* Back Button */}
      <div className="absolute top-4 left-4 z-10">
        <BackButton href="/" />
      </div>

      {/* Enhanced Hero Section */}
      <section className="relative overflow-hidden py-20">
        {/* Enhanced Background with Texture */}
        <div className="absolute inset-0 -z-10">
          {theme === 'dark' ? (
            <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black">
              {/* Texture Pattern for Dark Mode */}
              <div className="absolute inset-0 opacity-20" style={{
                backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0)`,
                backgroundSize: '20px 20px'
              }} />
              {/* Additional gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-blue-900/20 via-transparent to-purple-900/20" />
            </div>
          ) : (
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50">
              {/* Subtle texture for Light Mode */}
              <div className="absolute inset-0 opacity-30" style={{
                backgroundImage: `radial-gradient(circle at 1px 1px, rgba(59,130,246,0.1) 1px, transparent 0)`,
                backgroundSize: '24px 24px'
              }} />
            </div>
          )}
        </div>

        {/* Animated background shapes */}
        <div className="absolute inset-0 overflow-hidden opacity-20">
          <motion.div
            className="absolute top-20 left-10 w-64 h-64 rounded-full bg-blue-400 dark:bg-blue-600 blur-3xl"
            animate={{
              x: [0, 30, 0],
              y: [0, 20, 0],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              repeat: Infinity,
              duration: 8,
              ease: "easeInOut"
            }}
          />
          <motion.div
            className="absolute bottom-20 right-10 w-72 h-72 rounded-full bg-purple-400 dark:bg-purple-600 blur-3xl"
            animate={{
              x: [0, -30, 0],
              y: [0, -20, 0],
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              repeat: Infinity,
              duration: 10,
              ease: "easeInOut"
            }}
          />
        </div>

        <div className="container mx-auto px-4 py-20 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <motion.div
                initial={{ scale: 0.9 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="mb-6"
              >
                <FiGrid className={`w-16 h-16 mx-auto mb-6 ${
                  theme === 'dark' ? 'text-blue-400' : 'text-blue-600'
                }`} />
              </motion.div>

              <h1 className="text-5xl md:text-7xl font-bold mb-8 leading-tight bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 dark:from-blue-400 dark:via-purple-400 dark:to-pink-400">
                All Tools
              </h1>

              <p className="text-xl md:text-2xl mb-8 text-gray-700 dark:text-gray-300 max-w-4xl mx-auto leading-relaxed">
                Discover our complete collection of <span className="font-semibold text-blue-600 dark:text-blue-400">{totalToolCount}</span> powerful tools designed to simplify your workflow and boost productivity
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex flex-wrap justify-center gap-4 mb-12"
            >
              <div className="flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-blue-100 to-blue-50 dark:from-blue-900/30 dark:to-blue-800/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-700/50">
                <FiTool className="mr-2 w-5 h-5" /> {totalToolCount} Tools
              </div>
              <div className="flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-purple-100 to-purple-50 dark:from-purple-900/30 dark:to-purple-800/20 text-purple-700 dark:text-purple-300 border border-purple-200 dark:border-purple-700/50">
                <FiFileText className="mr-2 w-5 h-5" /> Easy to Use
              </div>
              <div className="flex items-center px-6 py-3 rounded-full bg-gradient-to-r from-green-100 to-green-50 dark:from-green-900/30 dark:to-green-800/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700/50">
                <FiDownload className="mr-2 w-5 h-5" /> Fast Processing
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="max-w-3xl mx-auto mb-16"
            >
              <div className="relative group">
                <div className={`absolute inset-0 rounded-3xl blur-xl transition-opacity duration-300 ${
                  theme === 'dark'
                    ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 group-hover:opacity-100 opacity-50'
                    : 'bg-gradient-to-r from-blue-200/50 to-purple-200/50 group-hover:opacity-100 opacity-30'
                }`} />

                <div className="relative">
                  <FiSearch className={`absolute left-6 top-1/2 transform -translate-y-1/2 w-6 h-6 transition-colors duration-300 ${
                    theme === 'dark' ? 'text-gray-400 group-hover:text-blue-400' : 'text-gray-500 group-hover:text-blue-600'
                  }`} />
                  <input
                    type="text"
                    placeholder="Search tools by name or description..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className={`w-full pl-16 pr-6 py-5 text-lg rounded-3xl border-2 transition-all duration-300 focus:outline-none focus:ring-4 ${
                      theme === 'dark'
                        ? 'bg-gray-800/80 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 focus:ring-blue-500/20 backdrop-blur-sm'
                        : 'bg-white/80 border-gray-200 text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20 backdrop-blur-sm'
                    }`}
                  />
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Wave divider */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 120" className="w-full h-auto">
            <path
              fill="currentColor"
              fillOpacity="1"
              className="text-gray-50 dark:text-gray-800"
              d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,120L1360,120C1280,120,1120,120,960,120C800,120,640,120,480,120C320,120,160,120,80,120L0,120Z"
            ></path>
          </svg>
        </div>
      </section>

      <section id="tools-section" className="py-16 transition-colors duration-300" style={{
        backgroundColor: 'var(--bg-secondary)'
      }}>
        <div className="container mx-auto px-4">
          <motion.h2
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-3xl md:text-4xl font-bold mb-12 text-center"
            style={{ color: 'var(--text-primary)' }}
          >
            Our Tools Collection
          </motion.h2>

          {searchQuery.trim() !== '' && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="mb-8 text-center"
            >
              <p style={{ color: 'var(--text-secondary)' }}>
                {filteredTools.length === 0 ? (
                  'No tools found matching your search.'
                ) : (
                  `Found ${filteredTools.length} tool${filteredTools.length === 1 ? '' : 's'} matching "${searchQuery}"`
                )}
              </p>
            </motion.div>
          )}

          <motion.div
            initial="hidden"
            animate="visible"
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.05,
                  delayChildren: 0.1
                }
              }
            }}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8"
          >
            {filteredTools.map((tool, index) => (
              <UnifiedCard
                key={tool.id}
                id={tool.id}
                title={tool.title}
                description={tool.description}
                icon={tool.icon}
                type="tool"
                category={tool.category}
                inputFormat={tool.inputFormat}
                outputFormat={tool.outputFormat}
                popular={tool.popular}
                index={index}
                variant="enhanced"
              />
            ))}
          </motion.div>

          {filteredComingSoonTools.length > 0 && (
            <>
              <motion.h2
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="text-2xl font-bold mt-16 mb-8 text-center"
                style={{ color: 'var(--text-primary)' }}
              >
                Coming Soon
              </motion.h2>

              <motion.div
                initial="hidden"
                animate="visible"
                variants={{
                  hidden: { opacity: 0 },
                  visible: {
                    opacity: 1,
                    transition: {
                      staggerChildren: 0.05,
                      delayChildren: 0.4
                    }
                  }
                }}
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8"
              >
                {filteredComingSoonTools.map((tool, index) => (
                  <UnifiedCard
                    key={tool.id}
                    id={tool.id}
                    title={tool.title}
                    description={tool.description}
                    icon={tool.icon}
                    type="tool"
                    category={tool.category}
                    inputFormat={tool.inputFormat}
                    outputFormat={tool.outputFormat}
                    popular={tool.popular}
                    comingSoon={true}
                    index={index}
                    delay={0.05 * (index % 8) + 0.4}
                    variant="enhanced"
                  />
                ))}
              </motion.div>
            </>
          )}
        </div>
      </section>

      {/* Recent Blog Posts Section */}
      <RecentBlogPosts limit={3} />

      {/* Footer */}
      <Footer />
    </main>
  );
}
