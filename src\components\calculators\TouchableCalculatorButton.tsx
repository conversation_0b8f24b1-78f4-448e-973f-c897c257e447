'use client';

import React, { forwardRef, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { TouchableButton } from '@/components/ui/TouchableButton';
import { hapticFeedback } from '@/hooks/useTouch';
import { cn } from '@/lib/utils';

interface TouchableCalculatorButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  onLongPress?: () => void;
  className?: string;
  disabled?: boolean;
  variant?: 'number' | 'operator' | 'equals' | 'clear' | 'function';
  size?: 'sm' | 'md' | 'lg';
  value?: string | number;
  isPressed?: boolean;
  enableContinuousPress?: boolean;
  continuousPressDelay?: number;
}

const TouchableCalculatorButton = forwardRef<HTMLButtonElement, TouchableCalculatorButtonProps>(
  (
    {
      children,
      onClick,
      onLongPress,
      className,
      disabled = false,
      variant = 'number',
      size = 'md',
      value,
      isPressed = false,
      enableContinuousPress = false,
      continuousPressDelay = 150,
      ...props
    },
    ref
  ) => {
    const intervalRef = useRef<NodeJS.Timeout | null>(null);
    const timeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Handle continuous press for operations like backspace
    const handleLongPress = () => {
      if (enableContinuousPress && onClick) {
        // Start continuous pressing after long press
        intervalRef.current = setInterval(() => {
          onClick();
          hapticFeedback.light();
        }, continuousPressDelay);
      }
      onLongPress?.();
    };

    const handleTouchEnd = () => {
      // Clear continuous press interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };

    // Cleanup on unmount
    useEffect(() => {
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
    }, []);

    // Variant-specific styles
    const getVariantStyles = () => {
      switch (variant) {
        case 'number':
          return 'bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600 hover:from-gray-100 hover:to-gray-200 dark:hover:from-gray-600 dark:hover:to-gray-700';
        case 'operator':
          return 'bg-gradient-to-br from-blue-500 to-blue-600 text-white border border-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-lg';
        case 'equals':
          return 'bg-gradient-to-br from-green-500 to-green-600 text-white border border-green-600 hover:from-green-600 hover:to-green-700 shadow-lg';
        case 'clear':
          return 'bg-gradient-to-br from-red-500 to-red-600 text-white border border-red-600 hover:from-red-600 hover:to-red-700 shadow-lg';
        case 'function':
          return 'bg-gradient-to-br from-purple-500 to-purple-600 text-white border border-purple-600 hover:from-purple-600 hover:to-purple-700 shadow-lg';
        default:
          return 'bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600';
      }
    };

    // Size-specific styles
    const getSizeStyles = () => {
      switch (size) {
        case 'sm':
          return 'h-12 w-12 text-sm font-semibold';
        case 'md':
          return 'h-16 w-16 text-lg font-semibold';
        case 'lg':
          return 'h-20 w-20 text-xl font-bold';
        default:
          return 'h-16 w-16 text-lg font-semibold';
      }
    };

    const handleTap = () => {
      // Provide appropriate haptic feedback based on button type
      switch (variant) {
        case 'number':
          hapticFeedback.light();
          break;
        case 'operator':
        case 'function':
          hapticFeedback.medium();
          break;
        case 'equals':
          hapticFeedback.success();
          break;
        case 'clear':
          hapticFeedback.heavy();
          break;
        default:
          hapticFeedback.light();
      }
      onClick?.();
    };

    return (
      <TouchableButton
        ref={ref}
        onTap={handleTap}
        onLongPress={handleLongPress}
        disabled={disabled}
        isPressed={isPressed}
        className={cn(
          'calculator-button relative overflow-hidden transition-all duration-150 ease-out',
          'select-none touch-manipulation',
          'focus:outline-none focus:ring-2 focus:ring-offset-2',
          getVariantStyles(),
          getSizeStyles(),
          {
            'focus:ring-blue-500': variant === 'operator',
            'focus:ring-green-500': variant === 'equals',
            'focus:ring-red-500': variant === 'clear',
            'focus:ring-purple-500': variant === 'function',
            'focus:ring-gray-500': variant === 'number',
          },
          className
        )}
        variant="calculator"
        size={size}
        touchFeedback="scale"
        {...props}
        onTouchEnd={handleTouchEnd}
      >
        <span className="relative z-10 flex items-center justify-center w-full h-full">
          {children}
        </span>

        {/* Active state indicator */}
        {isPressed && (
          <motion.div
            initial={{ scale: 0, opacity: 0.8 }}
            animate={{ scale: 1, opacity: 0.3 }}
            exit={{ scale: 0, opacity: 0 }}
            className="absolute inset-0 bg-white rounded-inherit"
          />
        )}

        {/* Continuous press indicator */}
        {enableContinuousPress && intervalRef.current && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute top-1 right-1 w-2 h-2 bg-yellow-400 rounded-full"
          />
        )}

        {/* Ripple effect for touch feedback */}
        <motion.div
          className="absolute inset-0 rounded-inherit pointer-events-none"
          initial={{ scale: 0, opacity: 0 }}
          whileTap={{ scale: 1.5, opacity: 0.3 }}
          transition={{ duration: 0.2 }}
          style={{
            background: variant === 'number' 
              ? 'radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%)'
              : 'radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%)'
          }}
        />
      </TouchableButton>
    );
  }
);

TouchableCalculatorButton.displayName = 'TouchableCalculatorButton';

export { TouchableCalculatorButton };
export type { TouchableCalculatorButtonProps };
