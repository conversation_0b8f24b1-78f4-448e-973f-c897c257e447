"use client";

import { motion } from "framer-motion";
import { Grid3X3, Columns } from "lucide-react";
import { Button } from "@/components/ui/button";

export type LayoutType = 'grid' | 'pinterest';

interface LayoutToggleProps {
  layoutType: LayoutType;
  onLayoutChange: (layout: LayoutType) => void;
  isDark?: boolean;
  className?: string;
}

export function LayoutToggle({ 
  layoutType, 
  onLayoutChange, 
  isDark = false,
  className = ""
}: LayoutToggleProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.6 }}
      className={`flex justify-center ${className}`}
    >
      <div className={`flex items-center gap-2 p-1 rounded-lg ${
        isDark 
          ? 'bg-gray-800/50 border border-gray-700 backdrop-blur-sm' 
          : 'bg-gray-100 border border-gray-200'
      }`}>
        <Button
          variant={layoutType === 'grid' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => onLayoutChange('grid')}
          className={`flex items-center gap-2 px-3 py-2 transition-all duration-300 ${
            layoutType === 'grid'
              ? (isDark ? 'bg-purple-600 text-white shadow-lg' : 'bg-blue-600 text-white shadow-lg')
              : (isDark ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-50')
          }`}
        >
          <Grid3X3 className="h-4 w-4" />
          <span className="hidden sm:inline">Grid Layout</span>
          <span className="sm:hidden">Grid</span>
        </Button>
        <Button
          variant={layoutType === 'pinterest' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => onLayoutChange('pinterest')}
          className={`flex items-center gap-2 px-3 py-2 transition-all duration-300 ${
            layoutType === 'pinterest'
              ? (isDark ? 'bg-purple-600 text-white shadow-lg' : 'bg-blue-600 text-white shadow-lg')
              : (isDark ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-50')
          }`}
        >
          <Columns className="h-4 w-4" />
          <span className="hidden sm:inline">Pinterest Style</span>
          <span className="sm:hidden">Pinterest</span>
        </Button>
      </div>
    </motion.div>
  );
}
