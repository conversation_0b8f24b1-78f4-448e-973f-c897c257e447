// lib/auth.ts
import { type NextAuthOptions, type DefaultSession } from "next-auth";
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import { connectToDatabase } from "./mongo";
import bcrypt from "bcryptjs";

// Type extensions for NextAuth
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      role: "admin" | "user";
      googleId?: string;
      emailVerified?: Date | null;
    } & DefaultSession["user"];
  }

  interface User {
    role?: "admin" | "user";
    googleId?: string;
    emailVerified?: Date | null;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role?: "admin" | "user";
    sub: string;
    googleId?: string;
    emailVerified?: Date | null;
  }
}

export const authOptions: NextAuthOptions = {
  // Use JWT strategy for sessions
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },

  // Authentication providers
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const { db } = await connectToDatabase();
          const normalizedEmail = credentials.email.toLowerCase();

          // Find user in MongoDB
          const user = await db.collection("users").findOne({
            email: normalizedEmail
          });

          if (!user) {
            return null;
          }

          // Verify password
          const isValid = await bcrypt.compare(credentials.password, user.password);

          if (!isValid) {
            return null;
          }

          // Return user object for NextAuth
          return {
            id: user._id.toString(),
            name: user.name,
            email: user.email,
            role: user.role || "user",
          };
        } catch (error) {
          console.error("Authentication error:", error);
          return null;
        }
      }
    })
  ],

  // Callbacks to add role to JWT and session
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider === "google") {
        try {
          const { db } = await connectToDatabase();
          const normalizedEmail = user.email?.toLowerCase();

          // Check if user exists
          let existingUser = await db.collection("users").findOne({
            email: normalizedEmail
          });

          if (!existingUser) {
            // Create new user with Google OAuth
            const newUser = {
              name: user.name,
              email: normalizedEmail,
              role: "user", // Default role for Google OAuth users
              googleId: account.providerAccountId,
              emailVerified: new Date(),
              image: user.image,
              createdAt: new Date(),
              updatedAt: new Date(),
            };

            const result = await db.collection("users").insertOne(newUser);
            user.role = "user";
            user.id = result.insertedId.toString();
          } else {
            // Update existing user with Google info if not already set
            const updateData: any = {};
            if (!existingUser.googleId) {
              updateData.googleId = account.providerAccountId;
            }
            if (!existingUser.emailVerified) {
              updateData.emailVerified = new Date();
            }
            if (user.image && !existingUser.image) {
              updateData.image = user.image;
            }
            updateData.updatedAt = new Date();

            if (Object.keys(updateData).length > 0) {
              await db.collection("users").updateOne(
                { _id: existingUser._id },
                { $set: updateData }
              );
            }

            user.role = existingUser.role || "user";
            user.id = existingUser._id.toString();
          }
        } catch (error) {
          console.error("Google OAuth sign-in error:", error);
          return false;
        }
      }
      return true;
    },
    jwt({ token, user, account }) {
      if (user) {
        token.role = user.role;
        token.googleId = user.googleId;
        token.emailVerified = user.emailVerified;
      }
      return token;
    },
    session({ session, token }) {
      if (session.user) {
        session.user.id = token.sub!;
        session.user.role = token.role ?? "user";
        session.user.googleId = token.googleId;
        session.user.emailVerified = token.emailVerified;
      }
      return session;
    }
  },

  // Custom pages
  pages: {
    signIn: "/login",
    error: "/login",
  },

  // Security
  secret: process.env.NEXTAUTH_SECRET!,
};
