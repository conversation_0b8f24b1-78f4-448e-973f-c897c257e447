import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import Comment from "@/models/Comment";
import mongoose from "mongoose";
import { z } from "zod";

// Schema validation for comment updates
const CommentUpdateSchema = z.object({
  content: z.string().min(3, "Comment must be at least 3 characters").optional(),
  status: z.enum(["pending", "approved", "spam", "trash"]).optional(),
  authorName: z.string().min(2, "Name is required").optional(),
  authorEmail: z.string().email("Valid email is required").optional(),
  authorWebsite: z.string().url().optional().nullable(),
});

// GET a single comment by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectToDatabase();

    const { id } = await params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: "Invalid comment ID format" },
        { status: 400 }
      );
    }

    // Find the comment
    const comment = await Comment.findById(id)
      .populate("userId", "name email")
      .populate("postId", "title slug");

    if (!comment) {
      return NextResponse.json(
        { error: "Comment not found" },
        { status: 404 }
      );
    }

    // If it's a parent comment, get its replies
    if (!comment.parentId) {
      const replies = await Comment.find({ parentId: comment._id, status: "approved" })
        .sort({ createdAt: 1 })
        .populate("userId", "name email");
      
      return NextResponse.json({
        ...comment.toObject(),
        replies
      });
    }

    return NextResponse.json(comment);
  } catch (error) {
    console.error(`GET /api/comments/${params.id} error:`, error);
    return NextResponse.json(
      { error: "Failed to fetch comment" },
      { status: 500 }
    );
  }
}

// PUT to update a comment
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectToDatabase();

    // Get user ID and role from request headers (set by middleware)
    const userId = request.headers.get("x-user-id");
    const userRole = request.headers.get("x-user-role");

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = await params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: "Invalid comment ID format" },
        { status: 400 }
      );
    }

    // Find the comment
    const comment = await Comment.findById(id);

    if (!comment) {
      return NextResponse.json(
        { error: "Comment not found" },
        { status: 404 }
      );
    }

    // Check permissions - only admin or the comment author can update
    if (userRole !== "admin" && comment.userId?.toString() !== userId) {
      return NextResponse.json(
        { error: "Forbidden - You don't have permission to update this comment" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validation = CommentUpdateSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: "Validation failed", details: validation.error },
        { status: 400 }
      );
    }

    // Regular users can only update content and website
    if (userRole !== "admin") {
      const { content, authorWebsite } = validation.data;
      const updatedComment = await Comment.findByIdAndUpdate(
        id,
        { $set: { content, authorWebsite } },
        { new: true }
      );
      return NextResponse.json(updatedComment);
    }

    // Admins can update all fields
    const updatedComment = await Comment.findByIdAndUpdate(
      id,
      { $set: validation.data },
      { new: true }
    );

    return NextResponse.json(updatedComment);
  } catch (error) {
    console.error(`PUT /api/comments/${params.id} error:`, error);
    return NextResponse.json(
      { error: "Failed to update comment" },
      { status: 500 }
    );
  }
}

// DELETE a comment
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectToDatabase();

    // Get user ID and role from request headers (set by middleware)
    const userId = request.headers.get("x-user-id");
    const userRole = request.headers.get("x-user-role");

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = await params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { error: "Invalid comment ID format" },
        { status: 400 }
      );
    }

    // Find the comment
    const comment = await Comment.findById(id);

    if (!comment) {
      return NextResponse.json(
        { error: "Comment not found" },
        { status: 404 }
      );
    }

    // Check permissions - only admin or the comment author can delete
    if (userRole !== "admin" && comment.userId?.toString() !== userId) {
      return NextResponse.json(
        { error: "Forbidden - You don't have permission to delete this comment" },
        { status: 403 }
      );
    }

    // For admins, permanently delete; for users, mark as trash
    if (userRole === "admin") {
      await Comment.findByIdAndDelete(id);
      
      // Also delete any replies to this comment
      await Comment.deleteMany({ parentId: id });
    } else {
      await Comment.findByIdAndUpdate(id, { status: "trash" });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(`DELETE /api/comments/${params.id} error:`, error);
    return NextResponse.json(
      { error: "Failed to delete comment" },
      { status: 500 }
    );
  }
}
