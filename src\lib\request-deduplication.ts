// lib/request-deduplication.ts - Request deduplication utility

interface PendingRequest {
  promise: Promise<any>;
  timestamp: number;
}

class RequestDeduplicator {
  private pendingRequests = new Map<string, PendingRequest>();
  private readonly TIMEOUT = 5000; // 5 seconds timeout

  /**
   * Deduplicate API requests by URL
   * If the same request is already in progress, return the existing promise
   */
  async deduplicate<T>(url: string, requestFn: () => Promise<T>): Promise<T> {
    const now = Date.now();
    
    // Clean up expired requests
    this.cleanup(now);
    
    // Check if request is already pending
    const existing = this.pendingRequests.get(url);
    if (existing) {
      console.log(`[DEDUP] Returning existing request for: ${url}`);
      return existing.promise;
    }

    // Create new request
    console.log(`[DEDUP] Creating new request for: ${url}`);
    const promise = requestFn().finally(() => {
      // Remove from pending requests when completed
      this.pendingRequests.delete(url);
    });

    // Store the pending request
    this.pendingRequests.set(url, {
      promise,
      timestamp: now
    });

    return promise;
  }

  /**
   * Clean up expired pending requests
   */
  private cleanup(now: number) {
    for (const [url, request] of this.pendingRequests.entries()) {
      if (now - request.timestamp > this.TIMEOUT) {
        console.log(`[DEDUP] Cleaning up expired request: ${url}`);
        this.pendingRequests.delete(url);
      }
    }
  }

  /**
   * Clear all pending requests
   */
  clear() {
    this.pendingRequests.clear();
  }

  /**
   * Get current pending request count
   */
  getPendingCount(): number {
    return this.pendingRequests.size;
  }
}

// Global instance
const requestDeduplicator = new RequestDeduplicator();

/**
 * Fetch with automatic deduplication
 */
export async function fetchWithDeduplication(url: string, options?: RequestInit): Promise<Response> {
  const requestKey = `${options?.method || 'GET'}:${url}:${JSON.stringify(options?.body || '')}`;
  
  return requestDeduplicator.deduplicate(requestKey, () => {
    return fetch(url, options);
  });
}

/**
 * API call with deduplication and JSON parsing
 */
export async function apiCallWithDeduplication<T = any>(url: string, options?: RequestInit): Promise<T> {
  const response = await fetchWithDeduplication(url, options);
  
  if (!response.ok) {
    throw new Error(`API call failed: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
}

/**
 * React hook for deduplicated API calls
 */
export function useDedupedFetch() {
  return {
    fetch: fetchWithDeduplication,
    apiCall: apiCallWithDeduplication,
    getPendingCount: () => requestDeduplicator.getPendingCount(),
    clearPending: () => requestDeduplicator.clear()
  };
}

export default requestDeduplicator;
