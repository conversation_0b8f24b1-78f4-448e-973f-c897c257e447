"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { FiArrowRight } from "react-icons/fi";
import { UniformBlogCard } from "@/components/home/<USER>";
import { Card, CardContent } from "@/components/ui/card";

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  featuredImage: string;
  publishedAt: string;
  author: {
    name: string;
    email: string;
  };
  category?: string;
  imageCredit?: string;
}

interface RecentBlogPostsProps {
  limit?: number;
}

export default function RecentBlogPosts({ limit = 3 }: RecentBlogPostsProps) {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRecentPosts = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/blog/recent?limit=${limit}`);
        const data = await response.json();
        if (data.success) {
          setPosts(data.data);
        } else {
          setError("Failed to load blog posts");
        }
      } catch (err) {
        console.error("Error fetching recent posts:", err);
        setError("Failed to load blog posts");
      } finally {
        setLoading(false);
      }
    };

    fetchRecentPosts();
  }, [limit]);

  // Container animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2,
      },
    },
  };

  // Button animation
  const buttonVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
    hover: {
      scale: 1.05,
      transition: {
        duration: 0.2,
      },
    },
  };

  if (loading) {
    return (
      <section className="py-20 container mx-auto px-4 md:px-6 bg-background">
        <h2 className="text-4xl font-extrabold text-center mb-14 text-foreground tracking-tight">
          Recent Blog Posts
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
          {[...Array(limit)].map((_, i) => (
            <div key={i} className="w-full max-w-sm mx-auto">
              <Card className="animate-pulse h-full">
                <div className="h-48 bg-muted rounded-t-lg"></div>
                <CardContent className="p-6">
                  <div className="h-4 bg-muted rounded mb-2"></div>
                  <div className="h-4 bg-muted rounded w-3/4 mb-4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </section>
    );
  }

  if (error || posts.length === 0) {
    return (
      <section className="py-20 container mx-auto px-4 md:px-6 bg-background">
        <motion.h2
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-4xl font-extrabold text-center mb-14 text-foreground tracking-tight"
        >
          Recent Blog Posts
        </motion.h2>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <Card className="max-w-md mx-auto">
            <CardContent className="p-8">
              <p className="text-muted-foreground mb-4">No blog posts found</p>
              <p className="text-sm text-muted-foreground">
                Check back later for new content!
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </section>
    );
  }

  return (
    <section className="py-20 container mx-auto px-4 md:px-6 bg-background border-t border-blue-100 rounded-t-3xl shadow-inner">
      <motion.h2
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.6 }}
        className="text-4xl font-extrabold text-center mb-14 text-foreground tracking-tight"
      >
        Recent Blog Posts
      </motion.h2>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="show"
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto"
      >
        {posts.map((post, index) => (
          <motion.div
            key={post.id}
            variants={{
              hidden: { opacity: 0, y: 30 },
              show: {
                opacity: 1,
                y: 0,
                transition: { type: "spring", stiffness: 100, damping: 15 },
              },
            }}
            className="w-full max-w-sm mx-auto h-full flex"
          >
            <UniformBlogCard
              post={post}
              index={index}
              showAnimation={false} // Disable internal animation since we're handling it here
              className="h-full w-full flex-1"
            />
          </motion.div>
        ))}
      </motion.div>

      <motion.div
        initial="hidden"
        animate="visible"
        variants={buttonVariants}
        className="mt-12 text-center"
      >
        <motion.div whileHover="hover" variants={buttonVariants}>
          <Link
            href="/blog"
            className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-full font-medium hover:bg-primary/90 transition-all shadow-md group"
          >
            <span>Read More Posts</span>
            <FiArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
          </Link>
        </motion.div>
      </motion.div>
    </section>
  );
}