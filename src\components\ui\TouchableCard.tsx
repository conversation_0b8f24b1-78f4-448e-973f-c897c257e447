'use client';

import React, { forwardRef } from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { useTouch, TouchHandlers } from '@/hooks/useTouch';
import { cn } from '@/lib/utils';

interface TouchableCardProps extends Omit<HTMLMotionProps<'div'>, keyof TouchHandlers> {
  children: React.ReactNode;
  onTap?: () => void;
  onDoubleTap?: () => void;
  onLongPress?: () => void;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  enableHapticFeedback?: boolean;
  longPressDelay?: number;
  swipeThreshold?: number;
  className?: string;
  disabled?: boolean;
  variant?: 'default' | 'elevated' | 'outlined' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  touchFeedback?: 'scale' | 'opacity' | 'both' | 'none';
  cardType?: 'blog' | 'tool' | 'calculator' | 'default';
  enableHoverAnimations?: boolean;
}

const TouchableCard = forwardRef<HTMLDivElement, TouchableCardProps>(
  (
    {
      children,
      onTap,
      onDoubleTap,
      onLongPress,
      onSwipeLeft,
      onSwipeRight,
      onSwipeUp,
      onSwipeDown,
      enableHapticFeedback = true,
      longPressDelay = 750,
      swipeThreshold = 50,
      className,
      disabled = false,
      variant = 'default',
      size = 'md',
      touchFeedback = 'scale',
      cardType = 'default',
      enableHoverAnimations = true,
      ...motionProps
    },
    ref
  ) => {
    const { touchState, touchHandlers, isTouchDevice } = useTouch(
      {
        onTap: disabled ? undefined : onTap,
        onDoubleTap: disabled ? undefined : onDoubleTap,
        onLongPress: disabled ? undefined : onLongPress,
        onSwipeLeft: disabled ? undefined : onSwipeLeft,
        onSwipeRight: disabled ? undefined : onSwipeRight,
        onSwipeUp: disabled ? undefined : onSwipeUp,
        onSwipeDown: disabled ? undefined : onSwipeDown,
      },
      {
        longPressDelay,
        swipeThreshold,
        enableHapticFeedback,
      }
    );

    // Variant styles
    const variantStyles = {
      default: 'bg-card border border-border shadow-sm hover:shadow-md',
      elevated: 'bg-card border border-border shadow-md hover:shadow-lg',
      outlined: 'bg-transparent border-2 border-border hover:bg-accent/5',
      ghost: 'bg-transparent hover:bg-accent/10',
    };

    // Size styles
    const sizeStyles = {
      sm: 'p-3 rounded-lg',
      md: 'p-4 rounded-xl',
      lg: 'p-6 rounded-2xl',
    };

    // Touch feedback and hover animations
    const getMotionProps = () => {
      if (disabled) return {};

      const isHoverDevice = typeof window !== 'undefined' &&
        window.matchMedia('(hover: hover) and (pointer: fine)').matches;

      const baseProps: any = {
        whileTap: {},
        animate: {},
      };

      // Touch feedback for touch devices
      if (!isHoverDevice && touchFeedback !== 'none') {
        if (touchFeedback === 'scale' || touchFeedback === 'both') {
          baseProps.whileTap.scale = 0.98;
          if (touchState.isLongPressed) {
            baseProps.animate.scale = 1.02;
          }
        }

        if (touchFeedback === 'opacity' || touchFeedback === 'both') {
          baseProps.whileTap.opacity = 0.8;
        }
      }

      // Hover animations for desktop devices
      if (isHoverDevice && enableHoverAnimations) {
        const hoverY = cardType === 'blog' ? -8 :
                       cardType === 'tool' ? -6 :
                       cardType === 'calculator' ? -4 : -5;

        const hoverScale = cardType === 'blog' ? 1.02 :
                          cardType === 'tool' ? 1.02 :
                          cardType === 'calculator' ? 1.01 : 1.02;

        // Enhanced shadow effects for different card types
        const hoverShadow = cardType === 'blog' ? '0 20px 40px rgba(0, 0, 0, 0.15)' :
                           cardType === 'tool' ? '0 15px 30px rgba(0, 0, 0, 0.12)' :
                           cardType === 'calculator' ? '0 12px 25px rgba(0, 0, 0, 0.1)' :
                           '0 10px 20px rgba(0, 0, 0, 0.1)';

        baseProps.whileHover = {
          y: hoverY,
          scale: hoverScale,
          boxShadow: hoverShadow,
          transition: { duration: 0.2, ease: "easeOut" }
        };

        // Disable touch feedback on hover devices
        baseProps.whileTap = {};
      }

      return baseProps;
    };

    // Gesture indicators
    const showSwipeIndicators = touchState.isSwiping && (onSwipeLeft || onSwipeRight);

    return (
      <motion.div
        ref={ref}
        className={cn(
          'touch-card relative transition-all duration-300 ease-out',
          'user-select-none cursor-pointer',
          variantStyles[variant],
          sizeStyles[size],
          {
            'opacity-50 cursor-not-allowed': disabled,
            'touch-active': touchState.isPressed && !disabled,
            'touch-long-press': touchState.isLongPressed && !disabled,
            'blog-card': cardType === 'blog' && enableHoverAnimations,
            'tool-card': cardType === 'tool' && enableHoverAnimations,
            'calculator-card': cardType === 'calculator' && enableHoverAnimations,
            'hover-effect': enableHoverAnimations,
            'hover-enabled': enableHoverAnimations && typeof window !== 'undefined' && window.matchMedia('(hover: hover) and (pointer: fine)').matches,
          },
          className
        )}
        {...touchHandlers}
        {...motionProps}
        {...getMotionProps()}
        style={{
          WebkitTouchCallout: 'none',
          WebkitUserSelect: 'none',
          WebkitTapHighlightColor: 'transparent',
          ...motionProps.style,
        }}
      >
        {children}

        {/* Swipe indicators */}
        {showSwipeIndicators && (
          <>
            {onSwipeLeft && (
              <div className="swipe-indicator left visible">
                <motion.div
                  initial={{ x: 10, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs"
                >
                  ←
                </motion.div>
              </div>
            )}
            {onSwipeRight && (
              <div className="swipe-indicator right visible">
                <motion.div
                  initial={{ x: -10, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs"
                >
                  →
                </motion.div>
              </div>
            )}
          </>
        )}

        {/* Long press indicator */}
        {touchState.isLongPressed && onLongPress && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="absolute top-2 right-2 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-xs"
          >
            ⋯
          </motion.div>
        )}

        {/* Touch ripple effect for touch devices */}
        {isTouchDevice && touchState.isPressed && !disabled && (
          <motion.div
            initial={{ scale: 0, opacity: 0.5 }}
            animate={{ scale: 2, opacity: 0 }}
            transition={{ duration: 0.6 }}
            className="absolute inset-0 bg-blue-500/20 rounded-inherit pointer-events-none"
          />
        )}
      </motion.div>
    );
  }
);

TouchableCard.displayName = 'TouchableCard';

export { TouchableCard };
export type { TouchableCardProps };
