import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import { BlogPost } from "@/models";

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    const { searchParams } = new URL(request.url);
    const parsedParams = {
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "6"),
      status: searchParams.get("status"),
      search: searchParams.get("search"),
      category: searchParams.get("category"),
    };

    const query: any = {};

    // Filter by status
    if (parsedParams.status && parsedParams.status !== 'all') {
      query.status = parsedParams.status;
    }

    // Filter by category
    if (parsedParams.category) {
      query.categoryId = parsedParams.category;
    }

    // Search functionality
    if (parsedParams.search) {
      query.$or = [
        { title: { $regex: parsedParams.search, $options: "i" } },
        { content: { $regex: parsedParams.search, $options: "i" } },
        { description: { $regex: parsedParams.search, $options: "i" } },
      ];
    }

    console.log("Admin blog query:", JSON.stringify(query, null, 2));

    const [posts, total] = await Promise.all([
      BlogPost.find(query)
        .sort({ createdAt: -1 })
        .skip((parsedParams.page - 1) * parsedParams.limit)
        .limit(parsedParams.limit)
        .populate("authorId", "name email")
        .populate("categoryId", "name slug")
        .lean(),
      BlogPost.countDocuments(query),
    ]);

    console.log(`Found ${posts.length} admin posts out of ${total} total`);

    // Format posts for admin interface
    const formattedPosts = posts.map(post => ({
      ...post,
      id: post._id.toString(),
      _id: post._id.toString(),
      author: {
        name: post.authorId?.name || "Admin User",
        email: post.authorId?.email || "<EMAIL>"
      },
      category: post.categoryName || post.categoryId?.name || "General",
      categoryId: post.categoryId || null,
      description: post.description || post.content?.substring(0, 150) + "..." || "",
      excerpt: post.description || post.content?.substring(0, 150) + "..." || "",
      featuredImage: post.featuredImage || "/images/blog-placeholder.jpg",
      publishedAt: post.publishedAt || post.createdAt,
      createdAt: post.createdAt,
    }));

    return NextResponse.json({
      success: true,
      data: formattedPosts,
      pagination: {
        total,
        page: parsedParams.page,
        limit: parsedParams.limit,
        totalPages: Math.ceil(total / parsedParams.limit),
      },
    });
  } catch (error) {
    console.error("GET /api/admin/blog/posts error:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch posts", data: [] },
      { status: 500 }
    );
  }
}
