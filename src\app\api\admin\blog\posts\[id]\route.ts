import { NextRequest, NextResponse } from "next/server";
import connectToDatabase from "@/lib/db";
import { BlogPost, Category } from "@/models";
import { handleBlogPostCategoryChange } from "@/lib/categorySync";

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectToDatabase();
    
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Post ID is required" },
        { status: 400 }
      );
    }

    // Find the post first to get category info for cleanup
    const post = await BlogPost.findById(id);
    if (!post) {
      return NextResponse.json(
        { error: "Post not found" },
        { status: 404 }
      );
    }

    // Store category info for cleanup
    const categoryId = post.categoryId;

    // Delete the post
    await BlogPost.findByIdAndDelete(id);

    // Update category post count if needed
    if (categoryId) {
      try {
        await handleBlogPostCategoryChange(null, categoryId.toString());
      } catch (error) {
        console.error('Error updating category count after deletion:', error);
        // Don't fail the deletion if category update fails
      }
    }

    return NextResponse.json({
      success: true,
      message: "Post deleted successfully"
    });

  } catch (error) {
    console.error("DELETE /api/admin/blog/posts/[id] error:", error);
    return NextResponse.json(
      { error: "Failed to delete post" },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectToDatabase();
    
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Post ID is required" },
        { status: 400 }
      );
    }

    const post = await BlogPost.findById(id)
      .populate("authorId", "name email")
      .populate("categoryId", "name slug")
      .lean();

    if (!post) {
      return NextResponse.json(
        { error: "Post not found" },
        { status: 404 }
      );
    }

    // Format post for admin interface
    const formattedPost = {
      ...post,
      id: post._id.toString(),
      _id: post._id.toString(),
      author: {
        name: post.authorId?.name || "Admin User",
        email: post.authorId?.email || "<EMAIL>"
      },
      category: post.categoryName || post.categoryId?.name || "General",
      categoryId: post.categoryId || null,
      description: post.description || post.content?.substring(0, 150) + "..." || "",
      excerpt: post.description || post.content?.substring(0, 150) + "..." || "",
      featuredImage: post.featuredImage || "/images/blog-placeholder.jpg",
      publishedAt: post.publishedAt || post.createdAt,
      createdAt: post.createdAt,
    };

    return NextResponse.json({
      success: true,
      data: formattedPost
    });

  } catch (error) {
    console.error("GET /api/admin/blog/posts/[id] error:", error);
    return NextResponse.json(
      { error: "Failed to fetch post" },
      { status: 500 }
    );
  }
}
