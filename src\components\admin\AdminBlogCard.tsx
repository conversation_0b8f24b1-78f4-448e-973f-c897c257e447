'use client';

import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Edit, Trash2, Eye, Calendar, User } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { ConfirmDialog } from '@/components/admin/ConfirmDialog';

interface BlogPost {
  _id: string;
  id?: string;
  title: string;
  description?: string;
  excerpt?: string;
  slug: string;
  featuredImage?: string;
  status: 'draft' | 'published' | 'scheduled' | 'archived';
  publishedAt?: string;
  createdAt: string;
  author: {
    name: string;
    email?: string;
  };
  categoryId?: {
    _id: string;
    name: string;
  };
  category?: string;
}

interface AdminBlogCardProps {
  post: BlogPost;
  index: number;
  onDelete: (postId: string) => void;
}

const statusColors = {
  draft: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
  published: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
  scheduled: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
  archived: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
};

export function AdminBlogCard({ post, index, onDelete }: AdminBlogCardProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await onDelete(post._id || post.id || '');
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Error deleting post:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'No date';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getDescription = () => {
    return post.description || post.excerpt || 'No description available';
  };

  const getCategoryName = () => {
    if (post.categoryId && typeof post.categoryId === 'object') {
      return post.categoryId.name;
    }
    return post.category || 'Uncategorized';
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: index * 0.1 }}
        whileHover={{ y: -4 }}
        className="group"
      >
        <Card className="h-full overflow-hidden hover:shadow-lg transition-all duration-300 border-border/50 hover:border-primary/30">
          <CardContent className="p-0">
            {/* Thumbnail Image */}
            <div className="relative h-32 overflow-hidden bg-muted">
              {post.featuredImage ? (
                <img
                  src={post.featuredImage}
                  alt={post.title}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-muted to-muted/50 flex items-center justify-center">
                  <div className="text-muted-foreground text-sm">No Image</div>
                </div>
              )}
              
              {/* Status Badge */}
              <div className="absolute top-2 right-2">
                <Badge className={statusColors[post.status]}>
                  {post.status}
                </Badge>
              </div>
            </div>

            {/* Content */}
            <div className="p-4 space-y-3">
              {/* Title */}
              <h1 className="font-bold text-lg leading-tight line-clamp-2 text-foreground group-hover:text-primary transition-colors">
                {post.title}
              </h1>

              {/* Description */}
              <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                {getDescription()}
              </p>

              {/* Meta Information */}
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  <span>{post.author?.name || 'Unknown'}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>{formatDate(post.publishedAt || post.createdAt)}</span>
                </div>
              </div>

              {/* Category */}
              <div className="text-xs">
                <Badge variant="outline" className="text-xs">
                  {getCategoryName()}
                </Badge>
              </div>

              {/* Action Icons */}
              <div className="flex items-center justify-end gap-2 pt-2 border-t border-border/50">
                <Button
                  variant="ghost"
                  size="sm"
                  asChild
                  className="h-8 w-8 p-0 hover:bg-primary/10 hover:text-primary"
                >
                  <Link href={`/blog/${post.slug}`} target="_blank">
                    <Eye className="h-4 w-4" />
                  </Link>
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  asChild
                  className="h-8 w-8 p-0 hover:bg-blue-500/10 hover:text-blue-600"
                >
                  <Link href={`/admin/blog/editor?id=${post._id || post.id}`}>
                    <Edit className="h-4 w-4" />
                  </Link>
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowDeleteDialog(true)}
                  className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        title="Delete Blog Post"
        description={`Are you sure you want to delete "${post.title}"? This action cannot be undone.`}
        onConfirm={handleDelete}
        isLoading={isDeleting}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
      />
    </>
  );
}
