import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { z } from "zod";
import connectToDatabase from "@/lib/db";
import User from "@/models/User";

const secret = process.env.NEXTAUTH_SECRET;

// Validation schema for user queries
const UserQuerySchema = z.object({
  role: z.enum(["admin", "user", "all"]).optional().default("all"),
  page: z.string().optional().default("1"),
  limit: z.string().optional().default("50"),
  search: z.string().optional(),
});

// Helper function to check admin authorization
async function checkAdminAuth(request: NextRequest) {
  const token = await getToken({ req: request, secret });
  
  if (!token || token.role !== "admin") {
    return NextResponse.json(
      { error: "Unauthorized. Admin access required." },
      { status: 401 }
    );
  }
  
  return null;
}

// GET /api/admin/users - List users with filtering
export async function GET(request: NextRequest) {
  try {
    // Check admin authorization
    const authError = await checkAdminAuth(request);
    if (authError) return authError;

    await connectToDatabase();

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validation = UserQuerySchema.safeParse(queryParams);
    if (!validation.success) {
      console.error("User query validation failed:", validation.error.issues);
      return NextResponse.json(
        { error: "Invalid query parameters", details: validation.error.issues },
        { status: 400 }
      );
    }

    const { role, page, limit, search } = validation.data;

    // Build filter query
    const filter: any = {};

    // Role filter
    if (role !== "all") {
      filter.role = role;
    }

    // Search filter
    if (search && search.trim()) {
      filter.$or = [
        { name: { $regex: search.trim(), $options: "i" } },
        { email: { $regex: search.trim(), $options: "i" } }
      ];
    }

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Execute queries
    const [users, totalCount] = await Promise.all([
      User.find(filter)
        .select("name email role createdAt updatedAt emailVerified googleId image")
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limitNum)
        .lean(),
      User.countDocuments(filter),
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    return NextResponse.json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalCount,
          limit: limitNum,
          hasNextPage,
          hasPrevPage,
        },
        filters: {
          role,
          search,
        },
      },
    });

  } catch (error) {
    console.error("Error fetching users:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/admin/users - Create a new user (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check admin authorization
    const authError = await checkAdminAuth(request);
    if (authError) return authError;

    await connectToDatabase();

    const body = await request.json();
    
    // Validate user data
    const UserCreateSchema = z.object({
      name: z.string().min(2).max(100),
      email: z.string().email(),
      password: z.string().min(8).optional(),
      role: z.enum(["user", "admin"]).optional().default("user"),
      googleId: z.string().optional(),
    });

    const validation = UserCreateSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Validation failed", details: validation.error.issues },
        { status: 400 }
      );
    }

    const userData = validation.data;

    // Check if email already exists
    const existingUser = await User.findOne({ email: userData.email.toLowerCase() });
    if (existingUser) {
      return NextResponse.json(
        { error: "Email already exists" },
        { status: 409 }
      );
    }

    // Create user
    const user = new User({
      ...userData,
      email: userData.email.toLowerCase(),
    });

    await user.save();

    // Return user without password
    const userResponse = await User.findById(user._id).select("-password");

    return NextResponse.json({
      success: true,
      data: userResponse,
      message: "User created successfully",
    });

  } catch (error) {
    console.error("Error creating user:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
