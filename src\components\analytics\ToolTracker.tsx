"use client";

import { useEffect } from 'react';
import { trackToolUsage, trackPageView } from '@/lib/analytics';

interface ToolTrackerProps {
  toolId: string;
  toolName: string;
  children: React.ReactNode;
}

/**
 * A component that was used to track tool usage - now disabled for performance
 * Kept for compatibility but no longer tracks anything
 */
export function ToolTracker({ toolId, toolName, children }: ToolTrackerProps) {
  // Analytics disabled - just render children
  return <>{children}</>;
}

/**
 * A hook to track tool completion
 * Call this function when a tool operation completes successfully
 */
export function useToolCompletion(toolId: string, toolName: string) {
  return {
    trackCompletion: (details?: Record<string, any>) => {
      trackToolUsage(toolId, toolName, 'completed', {
        ...details,
        timestamp: new Date().toISOString()
      });
    },
    trackFailure: (error?: any) => {
      trackToolUsage(toolId, toolName, 'failed', {
        error: error?.message || 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };
}

export default ToolTracker;
