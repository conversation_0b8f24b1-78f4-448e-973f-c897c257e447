/* Platform-specific optimizations for iOS and Android */

/* iOS Specific Styles */
.platform-ios {
  /* Fix iOS viewport height issues */
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
}

.platform-ios body {
  /* Disable iOS bounce scrolling */
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
}

.platform-ios input,
.platform-ios select,
.platform-ios textarea {
  /* Prevent zoom on input focus */
  font-size: 16px !important;
  transform: translateZ(0);
}

.platform-ios .touch-button,
.platform-ios .touch-card {
  /* iOS specific touch optimizations */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.platform-ios .calculator-button {
  /* iOS calculator button optimizations */
  -webkit-appearance: none;
  border-radius: 12px;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.platform-ios .mobile-menu {
  /* iOS safe area support */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

.platform-ios .pull-to-refresh {
  /* iOS pull to refresh optimization */
  -webkit-overflow-scrolling: touch;
  overscroll-behavior-y: contain;
}

/* Android Specific Styles */
.platform-android {
  /* Android optimizations */
  -webkit-tap-highlight-color: transparent;
}

.platform-android body {
  /* Android scrolling optimizations */
  overscroll-behavior: contain;
}

.platform-android .touch-button,
.platform-android .touch-card {
  /* Android touch optimizations */
  -webkit-tap-highlight-color: transparent;
  outline: none;
}

.platform-android .calculator-button {
  /* Android calculator button optimizations */
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.platform-android .mobile-menu {
  /* Android navigation bar handling */
  padding-bottom: 16px;
}

.platform-android .pull-to-refresh {
  /* Android pull to refresh */
  overscroll-behavior-y: auto;
}

/* Mobile Specific Styles */
.platform-mobile {
  /* Mobile optimizations */
  touch-action: manipulation;
}

.platform-mobile .touch-target {
  /* Ensure minimum touch target size on mobile */
  min-height: 44px;
  min-width: 44px;
}

.platform-mobile .blog-card {
  /* Mobile blog card optimizations */
  margin-bottom: 16px;
}

.platform-mobile .tool-card {
  /* Mobile tool card optimizations */
  padding: 16px;
}

.platform-mobile .calculator-grid {
  /* Mobile calculator grid */
  gap: 8px;
  padding: 16px;
}

/* Tablet Specific Styles */
.platform-tablet {
  /* Tablet optimizations */
  touch-action: manipulation;
}

.platform-tablet .touch-target {
  /* Larger touch targets for tablets */
  min-height: 48px;
  min-width: 48px;
}

.platform-tablet .blog-card {
  /* Tablet blog card optimizations */
  margin-bottom: 20px;
}

.platform-tablet .calculator-button {
  /* Tablet calculator buttons */
  height: 64px;
  width: 64px;
  font-size: 20px;
}

/* Touch Device Styles */
.platform-touch .hover-effect:hover {
  /* Disable hover effects on touch devices */
  transform: none !important;
  box-shadow: none !important;
}

.platform-touch .touch-feedback {
  /* Enhanced touch feedback */
  transition: transform 0.1s ease-out;
}

.platform-touch .touch-feedback:active {
  transform: scale(0.98);
}

/* Hover Capable Device Styles */
.platform-hover .touch-card:hover {
  /* Enable hover effects only on hover-capable devices */
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.platform-hover .calculator-button:hover {
  /* Hover effects for calculator buttons */
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* High DPI Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .touch-button,
  .touch-card {
    /* Crisp rendering on high DPI displays */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Landscape Orientation Optimizations */
@media (orientation: landscape) {
  .platform-mobile .mobile-menu {
    /* Adjust mobile menu for landscape */
    max-width: 50vw;
  }
  
  .platform-mobile .calculator-grid {
    /* Optimize calculator grid for landscape */
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Portrait Orientation Optimizations */
@media (orientation: portrait) {
  .platform-mobile .blog-grid {
    /* Single column for mobile portrait */
    grid-template-columns: 1fr;
  }
  
  .platform-tablet .blog-grid {
    /* Two columns for tablet portrait */
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Dark Mode Platform Optimizations */
@media (prefers-color-scheme: dark) {
  .platform-ios .calculator-button {
    /* iOS dark mode calculator buttons */
    background: linear-gradient(135deg, #2d3748, #1a202c);
    border-color: #4a5568;
  }
  
  .platform-android .calculator-button {
    /* Android dark mode calculator buttons */
    background: linear-gradient(135deg, #374151, #1f2937);
    border-color: #6b7280;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .platform-touch .touch-feedback,
  .platform-touch .touch-card,
  .platform-touch .calculator-button {
    transition: none !important;
    animation: none !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .touch-button,
  .touch-card,
  .calculator-button {
    border: 2px solid currentColor !important;
    background: transparent !important;
  }
}

/* Platform-specific scrollbar styles */
.platform-ios ::-webkit-scrollbar {
  display: none;
}

.platform-android ::-webkit-scrollbar {
  width: 4px;
}

.platform-android ::-webkit-scrollbar-track {
  background: transparent;
}

.platform-android ::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

/* Platform-specific focus styles */
.platform-ios .touch-button:focus,
.platform-ios .touch-card:focus {
  outline: 2px solid #007AFF;
  outline-offset: 2px;
}

.platform-android .touch-button:focus,
.platform-android .touch-card:focus {
  outline: 2px solid #4285F4;
  outline-offset: 2px;
}

/* Platform-specific animation performance */
.platform-mobile .touch-card,
.platform-mobile .calculator-button {
  /* Use GPU acceleration on mobile */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Platform-specific text rendering */
.platform-ios {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.platform-android {
  text-rendering: optimizeLegibility;
}

/* Platform-specific safe areas */
.platform-ios .header {
  padding-top: env(safe-area-inset-top);
}

.platform-ios .footer {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Platform-specific keyboard handling */
.platform-ios.keyboard-open {
  height: calc(var(--vh, 1vh) * 100 - var(--keyboard-height, 0px));
}

.platform-android.keyboard-open {
  /* Android handles keyboard differently */
  resize: none;
}
